"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { SearchIcon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import EventCard from "@/components/event/event-card"
import { searchEvents } from "@/lib/api/events"
import type { Event } from "@/lib/types"

export default function SearchPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<Event[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!searchQuery.trim()) return

    setIsLoading(true)
    setHasSearched(true)

    try {
      const results = await searchEvents(searchQuery)
      setSearchResults(results)
    } catch (error) {
      console.error("Search error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">搜索事件</h1>

      {/* 搜索表单 */}
      <form onSubmit={handleSearch} className="mb-8">
        <div className="flex gap-2 max-w-2xl">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="search"
              placeholder="搜索事件、节日、纪念日..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "搜索中..." : "搜索"}
          </Button>
        </div>
      </form>

      {/* 搜索结果 */}
      {hasSearched && (
        <div>
          <h2 className="text-xl font-bold mb-4">
            {searchQuery ? `"${searchQuery}" 的搜索结果` : "所有事件"}
            {searchResults.length > 0 && ` (${searchResults.length})`}
          </h2>

          {searchResults.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {searchResults.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">没有找到匹配的结果</p>
              <Button onClick={() => router.push("/calendar")}>浏览日历</Button>
            </div>
          )}
        </div>
      )}

      {/* 初始状态提示 */}
      {!hasSearched && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">输入关键词搜索事件、节日或纪念日</p>
          <p className="text-sm text-muted-foreground">例如：春节、元旦、清明节、国庆节等</p>
        </div>
      )}
    </div>
  )
}
