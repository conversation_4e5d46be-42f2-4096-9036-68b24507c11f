/**
 * Cloudflare Workers 适配器
 * 为 Next.js 静态导出提供边缘运行时支持
 */

// 导入静态资源映射
import manifest from './manifest.json'

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url)
    const pathname = url.pathname
    
    try {
      // 处理静态资源
      if (pathname.startsWith('/_next/static/')) {
        return await handleStaticAsset(request, env)
      }
      
      // 处理 API 路由
      if (pathname.startsWith('/api/')) {
        return await handleApiRoute(request, env, pathname)
      }
      
      // 处理页面路由
      return await handlePageRoute(request, env, pathname)
      
    } catch (error) {
      console.error('Worker error:', error)
      return new Response('Internal Server Error', { status: 500 })
    }
  }
}

/**
 * 处理静态资源
 */
async function handleStaticAsset(request, env) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  // 尝试从 R2 存储获取
  if (env.ASSETS_BUCKET) {
    try {
      const object = await env.ASSETS_BUCKET.get(pathname.slice(1))
      if (object) {
        const headers = new Headers()
        headers.set('Cache-Control', 'public, max-age=31536000, immutable')
        headers.set('Content-Type', getContentType(pathname))
        
        return new Response(object.body, { headers })
      }
    } catch (error) {
      console.error('R2 error:', error)
    }
  }
  
  // 降级到 404
  return new Response('Not Found', { status: 404 })
}

/**
 * 处理 API 路由
 */
async function handleApiRoute(request, env, pathname) {
  // Web Vitals API
  if (pathname === '/api/analytics/web-vitals') {
    return await handleWebVitalsApi(request, env)
  }
  
  // 健康检查
  if (pathname === '/api/health') {
    return new Response(JSON.stringify({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }), {
      headers: { 'Content-Type': 'application/json' }
    })
  }
  
  // 默认 404
  return new Response('API Not Found', { status: 404 })
}

/**
 * 处理页面路由
 */
async function handlePageRoute(request, env, pathname) {
  // 从缓存获取
  const cacheKey = `page:${pathname}`
  
  if (env.CACHE_KV) {
    try {
      const cached = await env.CACHE_KV.get(cacheKey)
      if (cached) {
        return new Response(cached, {
          headers: {
            'Content-Type': 'text/html',
            'Cache-Control': 'public, s-maxage=600, stale-while-revalidate=3600',
            'X-Cache': 'HIT'
          }
        })
      }
    } catch (error) {
      console.error('KV cache error:', error)
    }
  }
  
  // 生成页面内容
  const html = await generatePageHtml(pathname, env)
  
  // 存储到缓存
  if (env.CACHE_KV && html) {
    try {
      await env.CACHE_KV.put(cacheKey, html, { expirationTtl: 3600 })
    } catch (error) {
      console.error('KV cache set error:', error)
    }
  }
  
  return new Response(html, {
    headers: {
      'Content-Type': 'text/html',
      'Cache-Control': 'public, s-maxage=600, stale-while-revalidate=3600',
      'X-Cache': 'MISS'
    }
  })
}

/**
 * Web Vitals API 处理
 */
async function handleWebVitalsApi(request, env) {
  if (request.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 })
  }
  
  try {
    const data = await request.json()
    
    // 存储到 KV
    if (env.CACHE_KV) {
      const key = `metrics:${Date.now()}:${Math.random().toString(36).substring(2)}`
      await env.CACHE_KV.put(key, JSON.stringify(data), {
        expirationTtl: 86400 // 24小时
      })
    }
    
    // 检查性能阈值
    const alerts = checkPerformanceThresholds(data.metrics || [])
    
    return new Response(JSON.stringify({ 
      success: true,
      received: data.metrics?.length || 0,
      alerts
    }), {
      headers: { 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('Web Vitals API error:', error)
    return new Response(JSON.stringify({ error: 'Invalid request' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

/**
 * 生成页面 HTML
 */
async function generatePageHtml(pathname, env) {
  // 基础 HTML 模板
  const baseHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>营销日历 - 重要节日和营销节点</title>
  <meta name="description" content="查看重要节日、纪念日和营销节点，制定完美的营销策略">
  <link rel="stylesheet" href="/_next/static/css/app.css">
</head>
<body>
  <div id="__next">
    <div class="min-h-screen bg-background">
      <main class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">营销日历</h1>
        <div class="text-center py-12">
          <p class="text-lg text-muted-foreground">页面正在加载中...</p>
        </div>
      </main>
    </div>
  </div>
  <script src="/_next/static/js/app.js"></script>
</body>
</html>`
  
  return baseHtml
}

/**
 * 检查性能阈值
 */
function checkPerformanceThresholds(metrics) {
  const thresholds = {
    LCP: 2500,
    FID: 100,
    CLS: 0.1,
    FCP: 1800,
    TTFB: 800
  }
  
  const alerts = []
  
  for (const metric of metrics) {
    const threshold = thresholds[metric.name]
    if (threshold && metric.value > threshold) {
      alerts.push({
        metric: metric.name,
        value: metric.value,
        threshold,
        severity: metric.value > threshold * 2 ? 'high' : 'medium'
      })
    }
  }
  
  return alerts
}

/**
 * 获取内容类型
 */
function getContentType(pathname) {
  const ext = pathname.split('.').pop()
  const types = {
    'js': 'application/javascript',
    'css': 'text/css',
    'html': 'text/html',
    'json': 'application/json',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'woff': 'font/woff',
    'woff2': 'font/woff2',
    'ttf': 'font/ttf',
    'eot': 'application/vnd.ms-fontobject'
  }
  
  return types[ext] || 'application/octet-stream'
}
