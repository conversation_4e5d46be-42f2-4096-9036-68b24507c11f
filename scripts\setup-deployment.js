#!/usr/bin/env node

/**
 * 部署配置向导
 * 帮助用户设置 Cloudflare Workers 部署
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 营销日历网站部署配置向导\n')

// 检查必要工具
function checkPrerequisites() {
  console.log('📋 检查前置条件...')

  const tools = [
    { name: 'Node.js', command: 'node --version' },
    { name: 'npm', command: 'npm --version' },
    { name: 'Wrangler', command: 'npx wrangler --version' }
  ]

  for (const tool of tools) {
    try {
      const version = execSync(tool.command, { encoding: 'utf8' }).trim()
      console.log(`✅ ${tool.name}: ${version}`)
    } catch (error) {
      console.log(`❌ ${tool.name}: 未安装`)
      if (tool.name === 'Wrangler') {
        console.log('   正在安装 Wrangler...')
        execSync('npm install -g wrangler', { stdio: 'inherit' })
        console.log('✅ Wrangler 安装完成')
      }
    }
  }

  console.log('')
}

// 创建环境配置
function createEnvConfig() {
  console.log('⚙️ 环境配置设置')
  console.log('请提供以下信息来完成部署配置：\n')

  const envTemplate = `# 营销日历网站环境配置
# 生成时间: ${new Date().toISOString()}

# Supabase 配置 (必需)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Cloudflare 配置 (部署时需要)
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_ZONE_ID=your-zone-id
CLOUDFLARE_API_TOKEN=your-api-token

# 部署配置
NEXT_PUBLIC_BASE_URL=https://your-domain.com
REVALIDATION_SECRET=${generateRandomSecret()}

# 性能监控 (可选)
ALERT_WEBHOOK_URL=
PERFORMANCE_MONITORING_ENABLED=true

# 环境设置
NODE_ENV=production
DEBUG=false
`

  fs.writeFileSync('.env.local', envTemplate)
  console.log('✅ 已创建 .env.local 配置文件')
  console.log('📝 请编辑 .env.local 文件，填入您的实际配置值\n')
}

// 生成随机密钥
function generateRandomSecret() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15)
}

// 更新 wrangler.toml
function updateWranglerConfig() {
  console.log('🔧 更新 Cloudflare Workers 配置...')

  const wranglerConfig = `# Cloudflare Workers 配置
name = "marketing-calendar"
main = "workers/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = ["workers", "app", "components", "lib"]

# 环境变量
[vars]
NODE_ENV = "production"

# 开发环境
[env.development]
name = "marketing-calendar-dev"
workers_dev = true

[env.development.vars]
NODE_ENV = "development"

# 生产环境
[env.production]
name = "marketing-calendar-prod"
workers_dev = false

[env.production.vars]
NODE_ENV = "production"

# KV 存储绑定 (需要手动创建)
# [[env.production.kv_namespaces]]
# binding = "CACHE_KV"
# id = "your-kv-namespace-id"

# R2 存储绑定 (可选)
# [[env.production.r2_buckets]]
# binding = "ASSETS_BUCKET"
# bucket_name = "marketing-calendar-assets"

# 路由配置 (需要配置域名)
# [[env.production.routes]]
# pattern = "your-domain.com/*"
# zone_name = "your-domain.com"
`

  fs.writeFileSync('wrangler.toml', wranglerConfig)
  console.log('✅ 已更新 wrangler.toml 配置\n')
}

// 创建部署脚本
function createDeploymentScripts() {
  console.log('📜 创建部署脚本...')

  // 更新 package.json 脚本
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))

  packageJson.scripts = {
    ...packageJson.scripts,
    'deploy:dev': 'wrangler deploy --env development',
    'deploy:prod': 'wrangler deploy --env production',
    'setup:kv': 'wrangler kv:namespace create "CACHE_KV"',
    'setup:r2': 'wrangler r2 bucket create marketing-calendar-assets',
    'logs:dev': 'wrangler tail --env development',
    'logs:prod': 'wrangler tail --env production'
  }

  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2))
  console.log('✅ 已更新 package.json 部署脚本\n')
}

// 显示下一步指引
function showNextSteps() {
  console.log('🎯 下一步操作指引：\n')

  console.log('1. 📝 配置环境变量：')
  console.log('   编辑 .env.local 文件，填入您的 Supabase 和 Cloudflare 配置\n')

  console.log('2. 🔐 登录 Cloudflare：')
  console.log('   npx wrangler login\n')

  console.log('3. 🗄️ 创建 KV 存储：')
  console.log('   npm run setup:kv')
  console.log('   然后将返回的 ID 更新到 wrangler.toml 中\n')

  console.log('4. 🚀 部署到开发环境：')
  console.log('   npm run deploy:dev\n')

  console.log('5. 🌟 部署到生产环境：')
  console.log('   npm run deploy:prod\n')

  console.log('6. 📊 查看日志：')
  console.log('   npm run logs:dev  # 开发环境日志')
  console.log('   npm run logs:prod # 生产环境日志\n')

  console.log('📚 更多信息请查看：')
  console.log('   - Cloudflare Workers 文档: https://developers.cloudflare.com/workers/')
  console.log('   - Wrangler CLI 文档: https://developers.cloudflare.com/workers/wrangler/')
}

// 主函数
function main() {
  try {
    checkPrerequisites()
    createEnvConfig()
    updateWranglerConfig()
    createDeploymentScripts()
    showNextSteps()

    console.log('🎉 部署配置完成！')
    console.log('💡 提示：请先完成环境变量配置，然后运行部署命令')

  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()

// 检查必要工具
function checkPrerequisites() {
  console.log('📋 检查前置条件...')
  
  const tools = [
    { name: 'Node.js', command: 'node --version' },
    { name: 'npm', command: 'npm --version' },
    { name: 'Wrangler', command: 'npx wrangler --version' }
  ]
  
  for (const tool of tools) {
    try {
      const version = execSync(tool.command, { encoding: 'utf8' }).trim()
      console.log(`✅ ${tool.name}: ${version}`)
    } catch (error) {
      console.log(`❌ ${tool.name}: 未安装`)
      if (tool.name === 'Wrangler') {
        console.log('   正在安装 Wrangler...')
        execSync('npm install -g wrangler', { stdio: 'inherit' })
        console.log('✅ Wrangler 安装完成')
      }
    }
  }
  
  console.log('')
}

// 创建环境配置
function createEnvConfig() {
  console.log('⚙️ 环境配置设置')
  console.log('请提供以下信息来完成部署配置：\n')
  
  const envTemplate = `# 营销日历网站环境配置
# 生成时间: ${new Date().toISOString()}

# Supabase 配置 (必需)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# Cloudflare 配置 (部署时需要)
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_ZONE_ID=your-zone-id
CLOUDFLARE_API_TOKEN=your-api-token

# 部署配置
NEXT_PUBLIC_BASE_URL=https://your-domain.com
REVALIDATION_SECRET=${generateRandomSecret()}

# 性能监控 (可选)
ALERT_WEBHOOK_URL=
PERFORMANCE_MONITORING_ENABLED=true

# 环境设置
NODE_ENV=production
DEBUG=false
`

  fs.writeFileSync('.env.local', envTemplate)
  console.log('✅ 已创建 .env.local 配置文件')
  console.log('📝 请编辑 .env.local 文件，填入您的实际配置值\n')
}

// 生成随机密钥
function generateRandomSecret() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

// 更新 wrangler.toml
function updateWranglerConfig() {
  console.log('🔧 更新 Cloudflare Workers 配置...')
  
  const wranglerConfig = `# Cloudflare Workers 配置
name = "marketing-calendar"
main = "workers/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = ["workers", "app", "components", "lib"]

# 环境变量
[vars]
NODE_ENV = "production"

# 开发环境
[env.development]
name = "marketing-calendar-dev"
workers_dev = true

[env.development.vars]
NODE_ENV = "development"

# 生产环境
[env.production]
name = "marketing-calendar-prod"
workers_dev = false

[env.production.vars]
NODE_ENV = "production"

# KV 存储绑定 (需要手动创建)
# [[env.production.kv_namespaces]]
# binding = "CACHE_KV"
# id = "your-kv-namespace-id"

# R2 存储绑定 (可选)
# [[env.production.r2_buckets]]
# binding = "ASSETS_BUCKET"
# bucket_name = "marketing-calendar-assets"

# 路由配置 (需要配置域名)
# [[env.production.routes]]
# pattern = "your-domain.com/*"
# zone_name = "your-domain.com"
`

  fs.writeFileSync('wrangler.toml', wranglerConfig)
  console.log('✅ 已更新 wrangler.toml 配置\n')
}

// 创建部署脚本
function createDeploymentScripts() {
  console.log('📜 创建部署脚本...')
  
  // 更新 package.json 脚本
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  
  packageJson.scripts = {
    ...packageJson.scripts,
    'deploy:dev': 'wrangler deploy --env development',
    'deploy:prod': 'wrangler deploy --env production',
    'setup:kv': 'wrangler kv:namespace create "CACHE_KV"',
    'setup:r2': 'wrangler r2 bucket create marketing-calendar-assets',
    'logs:dev': 'wrangler tail --env development',
    'logs:prod': 'wrangler tail --env production'
  }
  
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2))
  console.log('✅ 已更新 package.json 部署脚本\n')
}

// 显示下一步指引
function showNextSteps() {
  console.log('🎯 下一步操作指引：\n')
  
  console.log('1. 📝 配置环境变量：')
  console.log('   编辑 .env.local 文件，填入您的 Supabase 和 Cloudflare 配置\n')
  
  console.log('2. 🔐 登录 Cloudflare：')
  console.log('   npx wrangler login\n')
  
  console.log('3. 🗄️ 创建 KV 存储：')
  console.log('   npm run setup:kv')
  console.log('   然后将返回的 ID 更新到 wrangler.toml 中\n')
  
  console.log('4. 🚀 部署到开发环境：')
  console.log('   npm run deploy:dev\n')
  
  console.log('5. 🌟 部署到生产环境：')
  console.log('   npm run deploy:prod\n')
  
  console.log('6. 📊 查看日志：')
  console.log('   npm run logs:dev  # 开发环境日志')
  console.log('   npm run logs:prod # 生产环境日志\n')
  
  console.log('📚 更多信息请查看：')
  console.log('   - Cloudflare Workers 文档: https://developers.cloudflare.com/workers/')
  console.log('   - Wrangler CLI 文档: https://developers.cloudflare.com/workers/wrangler/')
}

// 主函数
function main() {
  try {
    checkPrerequisites()
    createEnvConfig()
    updateWranglerConfig()
    createDeploymentScripts()
    showNextSteps()
    
    console.log('🎉 部署配置完成！')
    console.log('💡 提示：请先完成环境变量配置，然后运行部署命令')
    
  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main()
