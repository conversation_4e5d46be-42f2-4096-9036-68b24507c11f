import { createClient } from "@/lib/supabase/server"
import { initialCategories, initialTags, initialEvents } from "./seed-data"

export async function runMigrations() {
  const supabase = createClient()
  console.log("开始数据迁移...")

  try {
    // 1. 检查categories表是否为空
    const { data: existingCategories, error: categoriesError } = await supabase.from("categories").select("id").limit(1)

    if (categoriesError) throw categoriesError

    // 如果categories表为空，插入初始分类数据
    if (existingCategories.length === 0) {
      console.log("插入初始分类数据...")
      const { error } = await supabase.from("categories").insert(initialCategories)
      if (error) throw error
    }

    // 2. 检查tags表是否为空
    const { data: existingTags, error: tagsError } = await supabase.from("tags").select("id").limit(1)

    if (tagsError) throw tagsError

    // 如果tags表为空，插入初始标签数据
    if (existingTags.length === 0) {
      console.log("插入初始标签数据...")
      const { error } = await supabase.from("tags").insert(initialTags)
      if (error) throw error
    }

    // 3. 检查events表是否为空
    const { data: existingEvents, error: eventsError } = await supabase.from("events").select("id").limit(1)

    if (eventsError) throw eventsError

    // 如果events表为空，插入初始事件数据
    if (existingEvents.length === 0) {
      console.log("插入初始事件数据...")

      // 获取所有分类和标签的ID映射
      const { data: categories } = await supabase.from("categories").select("id, name")
      const { data: tags } = await supabase.from("tags").select("id, name")

      const categoryMap = new Map(categories.map((c) => [c.name, c.id]))
      const tagMap = new Map(tags.map((t) => [t.name, t.id]))

      // 准备事件数据，替换分类名称为分类ID
      const eventsWithCategoryIds = initialEvents.map((event) => ({
        ...event,
        category_id: categoryMap.get(event.category_name),
        // 删除category_name字段，因为数据库中没有这个字段
        category_name: undefined,
      }))

      // 插入事件数据
      for (const event of eventsWithCategoryIds) {
        const eventTags = event.tags || []
        delete event.tags

        // 插入事件
        const { data: newEvent, error: insertError } = await supabase.from("events").insert(event).select("id").single()

        if (insertError) throw insertError

        // 插入事件标签关联
        if (eventTags.length > 0 && newEvent) {
          const eventTagsData = eventTags
            .filter((tag) => tagMap.has(tag))
            .map((tag) => ({
              event_id: newEvent.id,
              tag_id: tagMap.get(tag),
            }))

          if (eventTagsData.length > 0) {
            const { error: tagLinkError } = await supabase.from("event_tags").insert(eventTagsData)

            if (tagLinkError) throw tagLinkError
          }
        }
      }
    }

    console.log("数据迁移完成!")
    return { success: true }
  } catch (error) {
    console.error("数据迁移失败:", error)
    return { success: false, error }
  }
}
