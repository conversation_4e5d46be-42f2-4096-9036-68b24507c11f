import Script from "next/script"

interface FAQStructuredDataProps {
  faq: Array<{
    question: string
    answer: string
  }>
  url: string
}

export default function FAQStructuredData({ faq, url }: FAQStructuredDataProps) {
  if (!faq || faq.length === 0) {
    return null
  }

  // 构建FAQ结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faq.map((item) => ({
      "@type": "Question",
      name: item.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: item.answer,
      },
    })),
  }

  return (
    <Script id={`faq-structured-data-${url}`} type="application/ld+json">
      {JSON.stringify(structuredData)}
    </Script>
  )
}
