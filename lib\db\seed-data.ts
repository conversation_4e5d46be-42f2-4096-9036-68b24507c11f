// 初始分类数据
export const initialCategories = [
  {
    id: "c1b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c2",
    name: "公历",
    slug: "gregorian",
    color: "#0891b2",
    priority: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c2b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c3",
    name: "农历",
    slug: "lunar",
    color: "#ca8a04",
    priority: 2,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c3b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c4",
    name: "重要事件",
    slug: "important-events",
    color: "#dc2626",
    priority: 3,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c4b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c5",
    name: "国际节日",
    slug: "international",
    color: "#7c3aed",
    priority: 4,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c5b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c6",
    name: "节气",
    slug: "solar-terms",
    color: "#15803d",
    priority: 5,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c6b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c7",
    name: "纪念日",
    slug: "memorial-days",
    color: "#4f46e5",
    priority: 6,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c7b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c8",
    name: "会展",
    slug: "exhibitions",
    color: "#06b6d4",
    priority: 7,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c8b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c9",
    name: "品牌日",
    slug: "brand-days",
    color: "#ea580c",
    priority: 8,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "c9b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3d0",
    name: "娱乐",
    slug: "entertainment",
    color: "#ec4899",
    priority: 9,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
]

// 初始标签数据
export const initialTags = [
  {
    id: "t1b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c2",
    name: "中国文化",
    slug: "chinese-culture",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t2b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c3",
    name: "传统文化",
    slug: "traditional-culture",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t3b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c4",
    name: "农历节日",
    slug: "lunar-festivals",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t4b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c5",
    name: "二十四节气",
    slug: "solar-terms",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t5b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c6",
    name: "法定假日",
    slug: "public-holidays",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t6b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c7",
    name: "公共假期",
    slug: "holidays",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t7b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c8",
    name: "国际文化",
    slug: "international-culture",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t8b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3c9",
    name: "全球庆典",
    slug: "global-celebrations",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t9b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3d0",
    name: "历史",
    slug: "history",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t10b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3d1",
    name: "纪念活动",
    slug: "memorial-activities",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: "t11b8c951-d8e7-4e8a-b9c8-d5f4a8e5b3d2",
    name: "祭祀",
    slug: "sacrifice",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
]

// 初始事件数据
export const initialEvents = [
  {
    name: "元旦",
    start_date: "2024-01-01",
    category_name: "公历",
    url_slug: "new-year-day",
    ai_summary: "元旦是公历新年的第一天，世界多数国家通用的节日。",
    priority: 10,
    status: "published",
    is_recurring: true,
    tags: ["法定假日", "公共假期"],
  },
  {
    name: "春节",
    start_date: "2024-02-10",
    lunar_date: "正月初一",
    category_name: "农历",
    url_slug: "spring-festival",
    ai_summary: "春节是中国最重要的传统节日，农历正月初一，象征着新的一年的开始。",
    priority: 10,
    status: "published",
    is_recurring: true,
    tags: ["中国文化", "传统文化", "农历节日", "法定假日"],
  },
  {
    name: "元宵节",
    start_date: "2024-02-24",
    lunar_date: "正月十五",
    category_name: "农历",
    url_slug: "lantern-festival",
    ai_summary: "元宵节是中国传统节日，农历正月十五，是春节之后的第一个重要节日。",
    priority: 8,
    status: "published",
    is_recurring: true,
    tags: ["中国文化", "传统文化", "农历节日"],
  },
  {
    name: "清明节",
    start_date: "2024-04-04",
    lunar_date: "二月廿五",
    category_name: "农历",
    url_slug: "qingming-festival",
    ai_summary: "清明节是中国传统节日，也是二十四节气之一，人们在这一天祭祖扫墓、踏青郊游。",
    priority: 9,
    status: "published",
    is_recurring: true,
    tags: ["传统文化", "农历节日", "二十四节气", "祭祀", "法定假日"],
  },
  {
    name: "劳动节",
    start_date: "2024-05-01",
    category_name: "公历",
    url_slug: "labor-day",
    ai_summary: "劳动节是国际性节日，旨在庆祝劳动者的贡献和成就。",
    priority: 9,
    status: "published",
    is_recurring: true,
    tags: ["法定假日", "公共假期"],
  },
  {
    name: "端午节",
    start_date: "2024-06-10",
    lunar_date: "五月初五",
    category_name: "农历",
    url_slug: "dragon-boat-festival",
    ai_summary: "端午节是中国传统节日，农历五月初五，主要活动包括赛龙舟、吃粽子等。",
    priority: 8,
    status: "published",
    is_recurring: true,
    tags: ["中国文化", "传统文化", "农历节日", "法定假日"],
  },
  {
    name: "中秋节",
    start_date: "2024-09-17",
    lunar_date: "八月十五",
    category_name: "农历",
    url_slug: "mid-autumn-festival",
    ai_summary: "中秋节是中国传统节日，农历八月十五，象征着团圆和丰收。",
    priority: 9,
    status: "published",
    is_recurring: true,
    tags: ["中国文化", "传统文化", "农历节日", "法定假日"],
  },
  {
    name: "国庆节",
    start_date: "2024-10-01",
    category_name: "公历",
    url_slug: "national-day",
    ai_summary: "国庆节是中华人民共和国成立纪念日，每年10月1日庆祝。",
    priority: 10,
    status: "published",
    is_recurring: true,
    tags: ["法定假日", "公共假期", "纪念活动"],
  },
]
