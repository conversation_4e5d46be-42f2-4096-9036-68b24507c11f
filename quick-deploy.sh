#!/bin/bash

# 快速部署脚本 - 适用于测试和开发环境
# 使用方法: ./quick-deploy.sh [域名]

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

DOMAIN=${1:-localhost}

log_info "🚀 开始快速部署日历应用..."
log_info "域名: $DOMAIN"

# 检查 Docker
if ! command -v docker &> /dev/null; then
    log_warning "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_warning "Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要目录
log_info "创建必要目录..."
mkdir -p nginx/logs nginx/ssl uploads

# 生成环境配置
log_info "生成环境配置..."
if [ ! -f .env ]; then
    cp .env.production .env
    
    # 生成随机密码和密钥
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    NEXTAUTH_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    REVALIDATION_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    
    # 替换配置文件中的占位符
    sed -i "s/your_secure_postgres_password_here/$POSTGRES_PASSWORD/g" .env
    sed -i "s/your_nextauth_secret_here_32_chars_min/$NEXTAUTH_SECRET/g" .env
    sed -i "s/your_revalidation_secret_here/$REVALIDATION_SECRET/g" .env
    sed -i "s/your-domain.com/$DOMAIN/g" .env
    
    log_success "环境配置文件已生成"
else
    log_info "使用现有环境配置文件"
fi

# 配置 Nginx
log_info "配置 Nginx..."
sed -i "s/your-domain.com/$DOMAIN/g" nginx/sites-available/calendar.conf

# 生成自签名 SSL 证书 (用于本地测试)
if [ ! -f nginx/ssl/fullchain.pem ]; then
    log_info "生成自签名 SSL 证书..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/privkey.pem \
        -out nginx/ssl/fullchain.pem \
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=$DOMAIN" \
        2>/dev/null
    log_success "SSL 证书已生成"
fi

# 停止现有服务
log_info "停止现有服务..."
docker-compose down --remove-orphans 2>/dev/null || true

# 构建和启动服务
log_info "构建和启动服务..."
docker-compose up -d --build

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."
if docker-compose ps | grep -q "Up"; then
    log_success "服务启动成功"
else
    log_warning "部分服务可能未正常启动，请检查日志"
    docker-compose logs --tail=20
fi

# 健康检查
log_info "执行健康检查..."
for i in {1..10}; do
    if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
        log_success "应用健康检查通过"
        break
    fi
    if [ $i -eq 10 ]; then
        log_warning "应用健康检查失败，请检查日志"
        docker-compose logs app --tail=20
    else
        sleep 5
    fi
done

# 显示部署信息
echo ""
log_success "🎉 快速部署完成！"
echo ""
echo "📋 访问信息:"
if [ "$DOMAIN" = "localhost" ]; then
    echo "  - HTTP:  http://localhost"
    echo "  - HTTPS: https://localhost (自签名证书)"
    echo "  - 应用:  http://localhost:3000"
else
    echo "  - HTTP:  http://$DOMAIN"
    echo "  - HTTPS: https://$DOMAIN"
fi
echo "  - 健康检查: http://localhost:3000/api/health"
echo ""
echo "🔧 管理命令:"
echo "  - 查看日志: docker-compose logs -f"
echo "  - 重启服务: docker-compose restart"
echo "  - 停止服务: docker-compose down"
echo ""
echo "📊 服务状态:"
docker-compose ps
echo ""

if [ "$DOMAIN" != "localhost" ]; then
    log_warning "注意: 如果使用真实域名，请确保:"
    echo "  1. 域名已正确解析到此服务器"
    echo "  2. 防火墙已开放 80 和 443 端口"
    echo "  3. 考虑使用 Let's Encrypt 申请正式 SSL 证书"
fi
