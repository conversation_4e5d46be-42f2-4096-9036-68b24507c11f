import { createServerClient } from "@supabase/ssr"
import { cookies } from "next/headers"
import type { Database } from "@/types/supabase"
import { createMockClient } from "./mock-client"
import { cache } from "react"

// 使用React的cache函数缓存Supabase客户端创建
export const createClient = cache(async () => {
  try {
    // 检查环境变量是否存在
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
      console.warn("Supabase URL或密钥缺失。使用模拟客户端代替。")
      return createMockClient()
    }

    // 创建实际的Supabase客户端
    try {
      const cookieStore = await cookies()
      return createServerClient<Database>(supabaseUrl, supabaseKey, {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set(name, value, options)
          },
          remove(name: string, options: any) {
            cookieStore.delete(name, options)
          },
        },
        // 添加性能优化设置
        db: {
          schema: "public",
        },
        global: {
          headers: {
            "x-application-name": "calendar-app",
          },
        },
        auth: {
          persistSession: false, // 在服务器环境中不持久化会话
          detectSessionInUrl: false,
          autoRefreshToken: false,
        },
      })
    } catch (clientError) {
      console.error("创建Supabase客户端时出错:", clientError)
      return createMockClient()
    }
  } catch (error) {
    console.warn("创建Supabase客户端时出错:", error)
    return createMockClient()
  }
})
