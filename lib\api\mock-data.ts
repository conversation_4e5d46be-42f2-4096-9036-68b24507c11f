import type { EventType } from "@/lib/types"

// 模拟分类数据
export function getMockCategories() {
  return [
    {
      id: "1",
      name: "公历" as EventType,
      slug: "gregorian",
      color: "#0891b2",
      priority: 1,
    },
    {
      id: "2",
      name: "农历" as EventType,
      slug: "lunar",
      color: "#ca8a04",
      priority: 2,
    },
    {
      id: "3",
      name: "重要事件" as EventType,
      slug: "important-events",
      color: "#dc2626",
      priority: 3,
    },
    {
      id: "4",
      name: "国际节日" as EventType,
      slug: "international",
      color: "#7c3aed",
      priority: 4,
    },
    {
      id: "5",
      name: "节气" as EventType,
      slug: "solar-terms",
      color: "#15803d",
      priority: 5,
    },
    {
      id: "6",
      name: "纪念日" as EventType,
      slug: "memorial-days",
      color: "#4f46e5",
      priority: 6,
    },
    {
      id: "7",
      name: "会展" as EventType,
      slug: "exhibitions",
      color: "#06b6d4",
      priority: 7,
    },
    {
      id: "8",
      name: "品牌日" as EventType,
      slug: "brand-days",
      color: "#ea580c",
      priority: 8,
    },
    {
      id: "9",
      name: "娱乐" as EventType,
      slug: "entertainment",
      color: "#ec4899",
      priority: 9,
    },
  ]
}

// 生成模拟事件数据
export function getMockEventsByMonth(year: number, month: number) {
  // 确保月份在有效范围内
  if (month < 1) {
    month = 12
    year--
  } else if (month > 12) {
    month = 1
    year++
  }

  // 基础事件数据
  const baseEvents = [
    {
      id: "1",
      name: "元旦",
      date: `${year}-01-01`,
      type: "公历" as EventType,
      lunarDate: undefined,
    },
    {
      id: "2",
      name: "春节",
      date: `${year}-02-10`,
      type: "农历" as EventType,
      lunarDate: "正月初一",
    },
    {
      id: "3",
      name: "元宵节",
      date: `${year}-02-24`,
      type: "农历" as EventType,
      lunarDate: "正月十五",
    },
    {
      id: "4",
      name: "国际妇女节",
      date: `${year}-03-08`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "5",
      name: "植树节",
      date: `${year}-03-12`,
      type: "纪念日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "qingming",
      name: "清明节",
      date: `${year}-04-04`,
      type: "农历" as EventType,
      lunarDate: "二月廿五",
    },
    {
      id: "7",
      name: "劳动节",
      date: `${year}-05-01`,
      type: "公历" as EventType,
      lunarDate: undefined,
    },
    {
      id: "8",
      name: "母亲节",
      date: `${year}-05-12`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "9",
      name: "端午节",
      date: `${year}-06-10`,
      type: "农历" as EventType,
      lunarDate: "五月初五",
    },
    {
      id: "10",
      name: "父亲节",
      date: `${year}-06-16`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "11",
      name: "中国共产党成立日",
      date: `${year}-07-01`,
      type: "纪念日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "12",
      name: "七夕节",
      date: `${year}-08-10`,
      type: "农历" as EventType,
      lunarDate: "七月初七",
    },
    {
      id: "13",
      name: "中秋节",
      date: `${year}-09-17`,
      type: "农历" as EventType,
      lunarDate: "八月十五",
    },
    {
      id: "14",
      name: "国庆节",
      date: `${year}-10-01`,
      type: "公历" as EventType,
      lunarDate: undefined,
    },
    {
      id: "15",
      name: "重阳节",
      date: `${year}-10-13`,
      type: "农历" as EventType,
      lunarDate: "九月初九",
    },
    {
      id: "16",
      name: "万圣节",
      date: `${year}-10-31`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "17",
      name: "双十一购物节",
      date: `${year}-11-11`,
      type: "品牌日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "18",
      name: "感恩节",
      date: `${year}-11-28`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "19",
      name: "圣诞节",
      date: `${year}-12-25`,
      type: "国际节日" as EventType,
      lunarDate: undefined,
    },
    {
      id: "20",
      name: "除夕",
      date: `${year}-01-31`,
      type: "农历" as EventType,
      lunarDate: "腊月三十",
    },
  ]

  // 为当前月份添加一些随机事件
  const daysInMonth = new Date(year, month, 0).getDate()
  const randomEvents = []

  for (let i = 1; i <= 3; i++) {
    const randomDay = Math.floor(Math.random() * daysInMonth) + 1
    const randomType: EventType = ["纪念日", "会展", "品牌日"][Math.floor(Math.random() * 3)] as EventType

    randomEvents.push({
      id: `random-${year}-${month}-${i}`,
      name: `${month}月随机事件 ${i}`,
      date: `${year}-${month.toString().padStart(2, "0")}-${randomDay.toString().padStart(2, "0")}`,
      type: randomType,
      description: `这是一个${month}月的随机事件示例。`,
    })
  }

  // 合并基础事件和随机事件
  const allEvents = [...baseEvents, ...randomEvents]

  // 只返回当前月份的事件
  return allEvents.filter((event) => {
    const eventMonth = new Date(event.date).getMonth() + 1
    return eventMonth === month
  })
}
