import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { getEventsByCategory, getCategories } from "@/lib/api/events"
import { getCategoryColor, getCategoryBgColor, getCategoryTextColor } from "@/lib/utils"
import EventCard from "@/components/event/event-card"

interface CategoryPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const categories = await getCategories()
  const category = categories.find((c) => c.slug === params.slug)

  if (!category) {
    return {
      title: "分类未找到",
      description: "抱歉，我们找不到您请求的分类。",
    }
  }

  return {
    title: `${category.name}事件列表 | 事件日历`,
    description: `浏览所有${category.name}类型的事件，包括日期、详情和相关信息。`,
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const categories = await getCategories()
  const category = categories.find((c) => c.slug === params.slug)

  if (!category) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">分类未找到</h1>
        <p className="mb-6">抱歉，我们找不到您请求的分类。</p>
        <Button asChild>
          <Link href="/calendar">返回日历</Link>
        </Button>
      </div>
    )
  }

  const events = await getEventsByCategory(params.slug)

  // 按日期排序
  events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

  // 将事件分组为过去、当前和未来
  const now = new Date()
  now.setHours(0, 0, 0, 0)

  const pastEvents = events.filter((event) => new Date(event.date) < now)
  const futureEvents = events.filter((event) => new Date(event.date) >= now)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center text-sm mb-6">
        <Link href="/" className="text-muted-foreground hover:text-foreground">
          首页
        </Link>
        <span className="mx-2 text-muted-foreground">/</span>
        <Link href="/categories" className="text-muted-foreground hover:text-foreground">
          分类
        </Link>
        <span className="mx-2 text-muted-foreground">/</span>
        <span className="text-foreground">{category.name}</span>
      </nav>

      {/* 返回按钮 */}
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/categories">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回分类列表
          </Link>
        </Button>
      </div>

      {/* 分类标题和信息 */}
      <div className="grid md:grid-cols-3 gap-8 mb-8">
        <div className="md:col-span-2">
          <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
            <Badge style={{ backgroundColor: getCategoryColor(category.name) }} className="text-white">
              {category.name}
            </Badge>
            <span>事件列表</span>
          </h1>
          <p className="text-muted-foreground mb-4">浏览所有{category.name}类型的事件，包括日期、详情和相关信息。</p>
        </div>

        <div>
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">事件类型</h3>
                  <div
                    className="px-3 py-2 rounded-md font-medium"
                    style={{
                      backgroundColor: getCategoryBgColor(category.name),
                      color: getCategoryTextColor(category.name),
                    }}
                  >
                    {category.name}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">事件总数</h3>
                  <div className="font-medium">{events.length} 个事件</div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">即将到来</h3>
                  <div className="font-medium">{futureEvents.length} 个事件</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 即将到来的事件 */}
      {futureEvents.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">即将到来的{category.name}事件</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {futureEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </div>
      )}

      {/* 过去的事件 */}
      {pastEvents.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold mb-6">过去的{category.name}事件</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {pastEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </div>
      )}

      {/* 没有事件的情况 */}
      {events.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">没有找到{category.name}类型的事件</p>
          <Button asChild>
            <Link href="/calendar">返回日历</Link>
          </Button>
        </div>
      )}
    </div>
  )
}
