import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'edge'

interface WebVitalMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType?: string
}

interface WebVitalData {
  metrics: WebVitalMetric[]
  url: string
  userAgent?: string
  timestamp: number
  sessionId?: string
}

export async function POST(request: NextRequest) {
  try {
    const data: WebVitalData = await request.json()
    
    // 验证数据
    if (!data.metrics || !Array.isArray(data.metrics)) {
      return NextResponse.json(
        { error: 'Invalid metrics data' },
        { status: 400 }
      )
    }
    
    // 在生产环境中，这里可以存储到数据库或分析服务
    if (process.env.NODE_ENV === 'production') {
      await storeMetrics(data)
    } else {
      // 开发环境只记录到控制台
      console.log('📊 Web Vitals Data:', {
        url: data.url,
        metrics: data.metrics.map(m => `${m.name}: ${m.value}ms (${m.rating})`),
        timestamp: new Date(data.timestamp).toISOString()
      })
    }
    
    // 检查性能阈值
    await checkPerformanceThresholds(data.metrics)
    
    return NextResponse.json({ 
      success: true,
      received: data.metrics.length 
    })
    
  } catch (error) {
    console.error('Web vitals API error:', error)
    return NextResponse.json(
      { error: 'Failed to process metrics' },
      { status: 500 }
    )
  }
}

async function storeMetrics(data: WebVitalData) {
  // 在实际部署中，这里可以：
  // 1. 存储到 Cloudflare KV
  // 2. 发送到 Cloudflare Analytics
  // 3. 存储到 Supabase
  // 4. 发送到第三方分析服务
  
  try {
    // 示例：存储到 KV（需要在 wrangler.toml 中配置 KV 绑定）
    // const key = `metrics:${Date.now()}:${Math.random().toString(36).substring(2)}`
    // await env.CACHE_KV.put(key, JSON.stringify(data), {
    //   expirationTtl: 86400 // 24小时过期
    // })
    
    console.log('📈 Metrics stored successfully')
  } catch (error) {
    console.error('Failed to store metrics:', error)
  }
}

async function checkPerformanceThresholds(metrics: WebVitalMetric[]) {
  const thresholds = {
    LCP: 2500, // 2.5s
    FID: 100,  // 100ms
    CLS: 0.1,  // 0.1
    FCP: 1800, // 1.8s
    TTFB: 800  // 800ms
  }
  
  for (const metric of metrics) {
    const threshold = thresholds[metric.name as keyof typeof thresholds]
    
    if (threshold && metric.value > threshold) {
      console.warn(`⚠️ Performance threshold exceeded: ${metric.name} = ${metric.value} (threshold: ${threshold})`)
      
      // 在生产环境中可以发送告警
      if (process.env.NODE_ENV === 'production') {
        await sendPerformanceAlert(metric, threshold)
      }
    }
  }
}

async function sendPerformanceAlert(metric: WebVitalMetric, threshold: number) {
  // 实现告警逻辑
  // 例如：发送到 Slack、邮件、Webhook 等
  
  const alertData = {
    type: 'performance_alert',
    metric: metric.name,
    value: metric.value,
    threshold,
    rating: metric.rating,
    timestamp: new Date().toISOString()
  }
  
  console.log('🚨 Performance Alert:', alertData)
  
  // 示例：发送到 Webhook
  // if (process.env.ALERT_WEBHOOK_URL) {
  //   await fetch(process.env.ALERT_WEBHOOK_URL, {
  //     method: 'POST',
  //     headers: { 'Content-Type': 'application/json' },
  //     body: JSON.stringify(alertData)
  //   })
  // }
}
