import { createClient } from "@/lib/supabase/server"

// 初始分类数据
const initialCategories = [
  {
    name: "公历",
    slug: "gregorian",
    color: "#0891b2",
    priority: 1,
  },
  {
    name: "农历",
    slug: "lunar",
    color: "#ca8a04",
    priority: 2,
  },
  {
    name: "重要事件",
    slug: "important-events",
    color: "#dc2626",
    priority: 3,
  },
  {
    name: "国际节日",
    slug: "international",
    color: "#7c3aed",
    priority: 4,
  },
  {
    name: "节气",
    slug: "solar-terms",
    color: "#15803d",
    priority: 5,
  },
  {
    name: "纪念日",
    slug: "memorial-days",
    color: "#4f46e5",
    priority: 6,
  },
  {
    name: "会展",
    slug: "exhibitions",
    color: "#06b6d4",
    priority: 7,
  },
  {
    name: "品牌日",
    slug: "brand-days",
    color: "#ea580c",
    priority: 8,
  },
  {
    name: "娱乐",
    slug: "entertainment",
    color: "#ec4899",
    priority: 9,
  },
]

// 初始事件数据 (简化版)
// This file contains the initial event data that will be inserted.
// You can modify the `initialEvents` array here to change the seed data.
const initialEvents = [
  {
    name: "元旦",
    start_date: "2024-01-01",
    category_name: "公历",
  },
  {
    name: "春节",
    start_date: "2024-02-10",
    lunar_date: "正月初一",
    category_name: "农历",
  },
  {
    name: "元宵节",
    start_date: "2024-02-24",
    lunar_date: "正月十五",
    category_name: "农历",
  },
  {
    name: "清明节",
    start_date: "2024-04-04",
    lunar_date: "二月廿五",
    category_name: "农历",
  },
  {
    name: "劳动节",
    start_date: "2024-05-01",
    category_name: "公历",
  },
  {
    name: "端午节",
    start_date: "2024-06-10",
    lunar_date: "五月初五",
    category_name: "农历",
  },
  {
    name: "中秋节",
    start_date: "2024-09-17",
    lunar_date: "八月十五",
    category_name: "农历",
  },
  {
    name: "国庆节",
    start_date: "2024-10-01",
    category_name: "公历",
  },
]

export async function runMinimalMigration() {
  const supabase = createClient()
  console.log("开始最小数据迁移...")

  try {
    // 1. 检查categories表是否为空
    const { data: existingCategories, error: categoriesError } = await supabase.from("categories").select("id").limit(1)

    if (categoriesError) throw categoriesError

    // 如果categories表为空，插入初始分类数据
    if (existingCategories.length === 0) {
      console.log("插入初始分类数据...")
      const { error } = await supabase.from("categories").insert(initialCategories)
      if (error) throw error
    }

    // 2. 检查events表是否为空
    const { data: existingEvents, error: eventsError } = await supabase.from("events").select("id").limit(1)

    if (eventsError) throw eventsError

    // 如果events表为空，插入初始事件数据
    if (existingEvents.length === 0) {
      console.log("插入初始事件数据...")

      // 获取所有分类的ID映射
      const { data: categories } = await supabase.from("categories").select("id, name")

      const categoryMap = new Map(categories.map((c) => [c.name, c.id]))

      // 准备事件数据，替换分类名称为分类ID
      const eventsWithCategoryIds = initialEvents.map((event) => ({
        name: event.name,
        start_date: event.start_date,
        lunar_date: event.lunar_date,
        category_id: categoryMap.get(event.category_name),
      }))

      // 插入事件数据
      const { error } = await supabase.from("events").insert(eventsWithCategoryIds)
      if (error) throw error
    }

    console.log("最小数据迁移完成!")
    return { success: true }
  } catch (error) {
    console.error("数据迁移失败:", error)
    return { success: false, error }
  }
}
