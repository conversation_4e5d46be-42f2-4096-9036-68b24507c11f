import { redirect } from "next/navigation"
import { createClient } from "@/lib/supabase/server"

export default async function AdminEventsPage() {
  const supabase = createClient()

  // 检查用户是否已认证并具有管理员权限
  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login?redirect=/admin/events")
  }

  // 检查用户角色
  const { data: userRole } = await supabase.from("profiles").select("role").eq("id", session.user.id).single()

  if (!userRole || userRole.role !== "admin") {
    redirect("/")
  }

  // 获取事件列表
  const { data: events } = await supabase
    .from("events")
    .select(`
      id,
      name,
      start_date,
      status,
      categories:category_id(name)
    `)
    .order("created_at", { ascending: false })

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">管理事件</h1>

      {/* 管理界面内容 */}
    </div>
  )
}
