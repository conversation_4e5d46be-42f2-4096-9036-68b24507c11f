import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    const supabase = createClient()
    console.log("开始创建数据库表...")

    // 创建categories表
    const { error: createCategoriesError } = await supabase.rpc("create_categories_table")

    if (createCategoriesError) {
      // 如果RPC不存在，尝试直接执行SQL
      const { error: sqlError } = await supabase
        .from("_exec_sql")
        .select("*")
        .eq(
          "query",
          `
        CREATE TABLE IF NOT EXISTS categories (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL UNIQUE,
          slug TEXT NOT NULL UNIQUE,
          color TEXT,
          priority INTEGER DEFAULT 999,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `,
        )

      if (sqlError) {
        console.error("创建categories表错误:", sqlError)
        return NextResponse.json(
          {
            error: "创建categories表失败，请在Supabase控制台手动创建表",
            details: sqlError.message,
          },
          { status: 500 },
        )
      }
    }

    // 创建events表
    const { error: createEventsError } = await supabase.rpc("create_events_table")

    if (createEventsError) {
      // 如果RPC不存在，尝试直接执行SQL
      const { error: sqlError } = await supabase
        .from("_exec_sql")
        .select("*")
        .eq(
          "query",
          `
        CREATE TABLE IF NOT EXISTS events (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          start_date DATE NOT NULL,
          lunar_date TEXT,
          category_id UUID NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          CONSTRAINT events_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id)
        );
      `,
        )

      if (sqlError) {
        console.error("创建events表错误:", sqlError)
        return NextResponse.json(
          {
            error: "创建events表失败，请在Supabase控制台手动创建表",
            details: sqlError.message,
          },
          { status: 500 },
        )
      }
    }

    console.log("数据库表创建完成!")
    return NextResponse.json({ success: true, message: "数据库表创建成功，请继续访问 /api/admin/setup 来填充数据" })
  } catch (error) {
    console.error("创建数据库表失败:", error)
    return NextResponse.json(
      {
        error: "创建数据库表失败，请在Supabase控制台手动创建表",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
