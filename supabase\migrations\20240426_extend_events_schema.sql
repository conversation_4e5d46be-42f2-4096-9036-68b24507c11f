-- 扩展事件表，添加新字段
ALTER TABLE events 
  ADD COLUMN IF NOT EXISTS end_date DATE,
  ADD COLUMN IF NOT EXISTS all_day BOOLEAN DEFAULT TRUE,
  ADD COLUMN IF NOT EXISTS start_time TIME,
  ADD COLUMN IF NOT EXISTS end_time TIME,
  ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'postponed', 'completed')),
  ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS recurrence_pattern TEXT DEFAULT 'none' CHECK (recurrence_pattern IN ('none', 'daily', 'weekly', 'monthly', 'yearly', 'custom')),
  ADD COLUMN IF NOT EXISTS recurrence_end_date DATE,
  ADD COLUMN IF NOT EXISTS custom_recurrence_rule TEXT,
  ADD COLUMN IF NOT EXISTS color TEXT,
  ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5,
  ADD COLUMN IF NOT EXISTS url TEXT,
  ADD COLUMN IF NOT EXISTS organizer TEXT,
  ADD COLUMN IF NOT EXISTS attendees TEXT[],
  ADD COLUMN IF NOT EXISTS reminder INTEGER[],
  ADD COLUMN IF NOT EXISTS attachments JSONB,
  ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT TRUE,
  ADD COLUMN IF NOT EXISTS source TEXT,
  ADD COLUMN IF NOT EXISTS image_url TEXT,
  ADD COLUMN IF NOT EXISTS related_events UUID[];

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_events_end_date ON events(end_date);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
CREATE INDEX IF NOT EXISTS idx_events_is_recurring ON events(is_recurring);
CREATE INDEX IF NOT EXISTS idx_events_priority ON events(priority);
CREATE INDEX IF NOT EXISTS idx_events_is_public ON events(is_public);

-- 更新搜索函数以包含新字段
CREATE OR REPLACE FUNCTION search_events(query_text TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  start_date DATE,
  end_date DATE,
  url_slug TEXT,
  category_id UUID,
  category_name TEXT,
  status TEXT,
  is_recurring BOOLEAN,
  rank REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id,
    e.name,
    e.start_date,
    e.end_date,
    e.url_slug,
    e.category_id,
    c.name AS category_name,
    e.status,
    e.is_recurring,
    ts_rank_cd(
      setweight(to_tsvector('simple', e.name), 'A') ||
      setweight(to_tsvector('simple', COALESCE(e.ai_summary, '')), 'B'),
      plainto_tsquery('simple', query_text)
    ) +
    similarity(e.name, query_text) * 2 +
    similarity(COALESCE(e.ai_summary, ''), query_text) AS rank
  FROM events e
  JOIN categories c ON e.category_id = c.id
  WHERE 
    e.status != 'cancelled' AND
    (
      e.name ILIKE '%' || query_text || '%' OR
      COALESCE(e.ai_summary, '') ILIKE '%' || query_text || '%' OR
      EXISTS (
        SELECT 1 FROM event_tags et
        JOIN tags t ON et.tag_id = t.id
        WHERE et.event_id = e.id AND t.name ILIKE '%' || query_text || '%'
      )
    )
  ORDER BY rank DESC, e.priority DESC
  LIMIT 20;
END;
$$ LANGUAGE plpgsql;
