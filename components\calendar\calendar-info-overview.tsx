"use client"

import { useMemo } from "react"
import type { Event, EventType } from "@/lib/types"
import { getCategoryBgColor, getCategoryTextColor } from "@/lib/utils"
import { DEFAULT_CATEGORIES } from "@/lib/types"

interface CalendarInfoOverviewProps {
  year: number
  month: number
  events: Event[]
  keyCategories: EventType[]
}

export default function CalendarInfoOverview({ year, month, events, keyCategories = [] }: CalendarInfoOverviewProps) {
  // Calculate date information directly with useMemo
  const dateInfo = useMemo(() => {
    const now = new Date()
    const currentYear = now.getFullYear()

    // Calculate current week number
    const startOfYear = new Date(currentYear, 0, 1)
    const millisecondsInDay = 24 * 60 * 60 * 1000
    const pastDaysOfYear = Math.floor((now.getTime() - startOfYear.getTime()) / millisecondsInDay)
    const currentWeek = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7)

    // Calculate days to next year
    const nextYear = new Date(currentYear + 1, 0, 1)
    const daysToNextYear = Math.ceil((nextYear.getTime() - now.getTime()) / millisecondsInDay)

    return {
      currentYear,
      currentWeek,
      daysToNextYear,
    }
  }, []) // Empty dependency array - calculate once

  // Calculate upcoming events directly with useMemo
  const upcomingEvents = useMemo(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const millisecondsInDay = 24 * 60 * 60 * 1000

    // Next year date for filtering
    const nextYearDate = new Date(today)
    nextYearDate.setFullYear(today.getFullYear() + 1)

    // 使用默认分类过滤事件
    const categoriesToUse = keyCategories.length > 0 ? keyCategories : DEFAULT_CATEGORIES

    // Filter events by key categories and future dates
    const keyEvents = events.filter((event) => {
      const eventDate = new Date(event.date)
      return categoriesToUse.includes(event.type) && eventDate >= today && eventDate <= nextYearDate
    })

    // Sort by date
    keyEvents.sort((a, b) => {
      const dateA = new Date(a.date)
      const dateB = new Date(b.date)
      return dateA.getTime() - dateB.getTime()
    })

    // Calculate days remaining for each event
    return keyEvents.slice(0, 5).map((event) => {
      const eventDate = new Date(event.date)
      const diffTime = eventDate.getTime() - today.getTime()
      const diffDays = Math.ceil(diffTime / millisecondsInDay)

      return {
        name: event.name,
        daysRemaining: diffDays,
        type: event.type,
      }
    })
  }, [events, keyCategories]) // Recalculate only when events or keyCategories change

  return (
    <div className="bg-muted/30 rounded-lg py-3 px-4 overflow-x-auto">
      <div className="flex items-center justify-between min-w-max">
        <div className="bg-slate-100 rounded-md px-3 py-1.5 text-sm whitespace-nowrap">
          <span className="font-medium">{dateInfo.currentYear}年第</span>
          <span className="text-orange-500 font-bold">{dateInfo.currentWeek}</span>
          <span className="font-medium">周，距{dateInfo.currentYear + 1}年还有</span>
          <span className="text-red-500 font-bold">{dateInfo.daysToNextYear}</span>
          <span className="font-medium">天</span>
        </div>

        <div className="flex items-center space-x-4 ml-4">
          {upcomingEvents.map((event, index) => (
            <div
              key={index}
              className="rounded-md px-3 py-1.5 text-sm whitespace-nowrap"
              style={{
                backgroundColor: getCategoryBgColor(event.type),
                color: getCategoryTextColor(event.type),
              }}
            >
              <span className="font-medium">距</span>
              <span className="font-bold">{event.name}</span>
              <span className="font-medium">还有</span>
              <span className="text-red-600 font-bold">{event.daysRemaining}</span>
              <span className="font-medium">天</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
