import Script from "next/script"

interface EventStructuredDataProps {
  event: {
    id: string
    name: string
    seoTitle?: string
    date: string
    metaDescription?: string
    content?: string
    location?: string
    type: string
    lunarDate?: string
  }
  url: string
}

export default function EventStructuredData({ event, url }: EventStructuredDataProps) {
  // 构建结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Event",
    name: event.seoTitle || event.name,
    description: event.metaDescription || event.content || `${event.name}是一个${event.type}类型的事件`,
    startDate: event.date,
    url: url,
    ...(event.location && {
      location: {
        "@type": "Place",
        name: event.location,
        address: {
          "@type": "PostalAddress",
          addressLocality: event.location,
        },
      },
    }),
  }

  return (
    <Script id={`structured-data-${event.id}`} type="application/ld+json">
      {JSON.stringify(structuredData)}
    </Script>
  )
}
