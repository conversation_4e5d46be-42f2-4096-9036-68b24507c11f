"use client"

import { motion, AnimatePresence, LayoutGroup } from "framer-motion"
import { useEffect, useState } from "react"
import { FILTER_ANIMATION, BRAND_VARIANTS } from "@/lib/animations/config"

interface FilterAnimationProps {
  children: React.ReactNode
  isVisible: boolean
  delay?: number
  layoutId?: string
  className?: string
}

// 单个事件的筛选动画
export function FilterableEvent({ 
  children, 
  isVisible, 
  delay = 0,
  layoutId,
  className = ""
}: FilterAnimationProps) {
  return (
    <AnimatePresence mode="popLayout">
      {isVisible && (
        <motion.div
          layoutId={layoutId}
          className={className}
          initial={{ 
            opacity: 0, 
            scale: 0.8,
            y: 10
          }}
          animate={{ 
            opacity: 1, 
            scale: 1,
            y: 0
          }}
          exit={{ 
            opacity: 0, 
            scale: 0.8,
            y: -10
          }}
          transition={{
            duration: 0.3,
            delay,
            ease: "easeOut",
            layout: {
              duration: 0.25,
              ease: "easeInOut"
            }
          }}
          layout
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 事件列表的错开动画容器
export function FilterableEventList({ 
  children,
  className = ""
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <LayoutGroup>
      <motion.div
        className={className}
        variants={BRAND_VARIANTS.stagger}
        initial="initial"
        animate="animate"
        layout
      >
        {children}
      </motion.div>
    </LayoutGroup>
  )
}

// 筛选器按钮动画
export function FilterButton({
  children,
  isActive,
  onClick,
  className = "",
  ...props
}: {
  children: React.ReactNode
  isActive: boolean
  onClick?: () => void
  className?: string
  [key: string]: any
}) {
  return (
    <motion.button
      className={className}
      onClick={onClick}
      whileHover={{ 
        scale: 1.05,
        transition: { duration: 0.2 }
      }}
      whileTap={{ 
        scale: 0.95,
        transition: { duration: 0.1 }
      }}
      animate={{
        scale: isActive ? 1.02 : 1,
        boxShadow: isActive 
          ? "0 4px 12px rgba(0, 0, 0, 0.15)" 
          : "0 1px 3px rgba(0, 0, 0, 0.1)"
      }}
      transition={{
        duration: 0.2,
        ease: "easeOut"
      }}
      {...props}
    >
      {children}
    </motion.button>
  )
}

// 筛选结果计数动画
export function FilterResultCount({ 
  count,
  total,
  className = ""
}: {
  count: number
  total: number
  className?: string
}) {
  const [displayCount, setDisplayCount] = useState(count)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDisplayCount(count)
    }, 150) // 等待筛选动画开始
    
    return () => clearTimeout(timer)
  }, [count])

  return (
    <motion.div
      className={className}
      key={displayCount} // 强制重新渲染动画
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      显示 <motion.span
        key={displayCount}
        initial={{ scale: 1.2, color: "hsl(var(--primary))" }}
        animate={{ scale: 1, color: "inherit" }}
        transition={{ duration: 0.3 }}
        className="font-semibold"
      >
        {displayCount}
      </motion.span> / {total} 个事件
    </motion.div>
  )
}

// 筛选状态指示器
export function FilterIndicator({
  isFiltering,
  activeFilters,
  onClear,
  className = ""
}: {
  isFiltering: boolean
  activeFilters: string[]
  onClear?: () => void
  className?: string
}) {
  return (
    <AnimatePresence>
      {isFiltering && activeFilters.length > 0 && (
        <motion.div
          className={`flex items-center gap-2 ${className}`}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <span className="text-sm text-muted-foreground">
            已筛选:
          </span>
          <div className="flex flex-wrap gap-1">
            {activeFilters.map((filter, index) => (
              <motion.span
                key={filter}
                className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ delay: index * 0.05 }}
              >
                {filter}
              </motion.span>
            ))}
          </div>
          {onClear && (
            <motion.button
              onClick={onClear}
              className="text-xs text-muted-foreground hover:text-foreground transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              清除
            </motion.button>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// 空状态动画
export function EmptyFilterState({
  message = "没有找到匹配的事件",
  className = ""
}: {
  message?: string
  className?: string
}) {
  return (
    <motion.div
      className={`text-center py-12 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, delay: 0.2 }}
    >
      <motion.div
        className="text-4xl mb-4"
        animate={{ 
          rotate: [0, 10, -10, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          repeatDelay: 3
        }}
      >
        🔍
      </motion.div>
      <p className="text-muted-foreground">{message}</p>
    </motion.div>
  )
}

// 筛选加载状态
export function FilterLoadingState({
  message = "正在筛选...",
  className = ""
}: {
  message?: string
  className?: string
}) {
  return (
    <motion.div
      className={`flex items-center justify-center py-8 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="flex items-center gap-2">
        <div className="flex space-x-1">
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-primary rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
        <span className="text-sm text-muted-foreground ml-2">{message}</span>
      </div>
    </motion.div>
  )
}
