import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { getCategoryColor } from "@/lib/utils"
import { EVENT_TYPE_PRIORITY } from "@/lib/types"

interface RelatedEventsProps {
  event: any
}

export default function RelatedEvents({ event }: RelatedEventsProps) {
  // 生成相关事件，如果API未提供则使用生成的示例数据
  const relatedEvents = event.relatedEvents || generateRelatedEvents(event)

  // 如果没有相关事件，显示占位信息
  if (relatedEvents.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        <p>相关事件信息即将上线，敬请期待。</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {relatedEvents.map((relEvent, index) => (
        <div key={index} className="border rounded-lg p-3 hover:bg-muted/50 transition-colors">
          <Link href={`/event/${relEvent.id}`} className="block">
            <div className="flex items-start gap-2">
              <Badge
                style={{ backgroundColor: getCategoryColor(relEvent.type) }}
                className="text-white shrink-0 mt-0.5"
              >
                {relEvent.type}
              </Badge>
              <div>
                <div className="font-medium">{relEvent.name}</div>
                <div className="text-sm text-muted-foreground">
                  {new Date(relEvent.date).toLocaleDateString("zh-CN", {
                    month: "long",
                    day: "numeric",
                  })}
                </div>
              </div>
            </div>
          </Link>
        </div>
      ))}
    </div>
  )
}

// 辅助函数，生成相关事件
function generateRelatedEvents(event: any): any[] {
  // 这里应该从数据库获取相关事件
  // 为了演示，我们生成一些示例数据

  // 获取所有可能的事件类型（按优先级排序）
  const eventTypes = Object.keys(EVENT_TYPE_PRIORITY)

  // 创建相关事件数组
  const relatedEvents = []

  // 添加2个相同类型的事件
  for (let i = 1; i <= 2; i++) {
    relatedEvents.push({
      id: `rel-same-${i}`,
      name: `相关${event.type}${i}`,
      date: new Date().toISOString(),
      type: event.type,
    })
  }

  // 添加4个不同类型的事件
  let count = 0
  for (const type of eventTypes) {
    if (type !== event.type && count < 4) {
      relatedEvents.push({
        id: `rel-diff-${count}`,
        name: `${type}示例事件`,
        date: new Date().toISOString(),
        type: type,
      })
      count++
    }
  }

  return relatedEvents
}
