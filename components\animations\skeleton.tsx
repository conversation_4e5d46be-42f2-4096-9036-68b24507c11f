"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { LOADING_ANIMATIONS, DURATIONS } from "@/lib/animations/config"

interface SkeletonProps {
  className?: string
  variant?: "default" | "pulse" | "shimmer"
  children?: React.ReactNode
}

// 基础骨架组件
export function Skeleton({ 
  className, 
  variant = "shimmer",
  children,
  ...props 
}: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  const baseClasses = "animate-pulse rounded-md bg-muted"
  
  if (variant === "shimmer") {
    return (
      <div
        className={cn(baseClasses, "relative overflow-hidden", className)}
        {...props}
      >
        <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent" />
        {children}
      </div>
    )
  }

  if (variant === "pulse") {
    return (
      <motion.div
        className={cn(baseClasses, className)}
        {...LOADING_ANIMATIONS.pulse}
        {...props}
      >
        {children}
      </motion.div>
    )
  }

  return (
    <div className={cn(baseClasses, className)} {...props}>
      {children}
    </div>
  )
}

// 日历网格骨架屏
export function CalendarGridSkeleton() {
  return (
    <div className="border rounded-lg overflow-hidden">
      {/* 星期标题行 */}
      <div className="grid grid-cols-7 bg-muted">
        {Array.from({ length: 7 }).map((_, i) => (
          <div key={i} className="py-2 text-center">
            <Skeleton className="h-4 w-6 mx-auto" />
          </div>
        ))}
      </div>

      {/* 日历网格 */}
      <div className="grid grid-cols-7 divide-x divide-y">
        {Array.from({ length: 42 }).map((_, i) => (
          <CalendarCellSkeleton key={i} delay={i * 0.02} />
        ))}
      </div>
    </div>
  )
}

// 日历单元格骨架
function CalendarCellSkeleton({ delay = 0 }: { delay?: number }) {
  return (
    <motion.div
      className="min-h-[100px] p-1"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay, duration: 0.3 }}
    >
      {/* 日期行 */}
      <div className="flex justify-between items-center mb-1">
        <Skeleton className="h-3 w-8" />
        <Skeleton className="h-4 w-4 rounded-full" />
      </div>

      {/* 事件骨架 */}
      <div className="space-y-1">
        {Array.from({ length: Math.floor(Math.random() * 3) + 1 }).map((_, i) => (
          <Skeleton 
            key={i} 
            className="h-4 rounded" 
            style={{ width: `${60 + Math.random() * 30}%` }}
          />
        ))}
      </div>
    </motion.div>
  )
}

// 事件卡片骨架
export function EventCardSkeleton({ delay = 0 }: { delay?: number }) {
  return (
    <motion.div
      className="flex items-start gap-2 p-2 rounded"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.3 }}
    >
      <Skeleton className="w-12 h-6 rounded" />
      <div className="flex-1 space-y-1">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </div>
    </motion.div>
  )
}

// 月份标题骨架
export function MonthTitleSkeleton() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-10 w-10 rounded-md" />
      <Skeleton className="h-6 w-32" />
      <Skeleton className="h-10 w-10 rounded-md" />
    </div>
  )
}

// 筛选器骨架
export function FilterSkeleton() {
  return (
    <div className="flex flex-wrap gap-2">
      {Array.from({ length: 5 }).map((_, i) => (
        <Skeleton key={i} className="h-8 w-16 rounded-full" />
      ))}
    </div>
  )
}

// 信息概览骨架
export function InfoOverviewSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {Array.from({ length: 3 }).map((_, i) => (
        <motion.div
          key={i}
          className="bg-card rounded-lg p-4 border"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: i * 0.1, duration: 0.4 }}
        >
          <div className="flex items-center justify-between mb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-4 rounded" />
          </div>
          <Skeleton className="h-8 w-16 mb-1" />
          <Skeleton className="h-3 w-24" />
        </motion.div>
      ))}
    </div>
  )
}

// 加载进度条
export function LoadingProgressBar({ 
  progress = 0,
  className = ""
}: { 
  progress?: number
  className?: string 
}) {
  return (
    <div className={cn("w-full bg-muted rounded-full h-1", className)}>
      <motion.div
        className="bg-primary h-1 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      />
    </div>
  )
}

// 智能加载指示器
export function SmartLoader({ 
  type = "skeleton",
  message = "加载中...",
  progress,
  className = ""
}: {
  type?: "skeleton" | "progress" | "dots"
  message?: string
  progress?: number
  className?: string
}) {
  if (type === "progress" && progress !== undefined) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="text-sm text-muted-foreground text-center">{message}</div>
        <LoadingProgressBar progress={progress} />
      </div>
    )
  }

  if (type === "dots") {
    return (
      <div className={cn("flex items-center justify-center space-x-1", className)}>
        <div className="text-sm text-muted-foreground mr-2">{message}</div>
        {Array.from({ length: 3 }).map((_, i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-primary rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.2
            }}
          />
        ))}
      </div>
    )
  }

  return <CalendarGridSkeleton />
}
