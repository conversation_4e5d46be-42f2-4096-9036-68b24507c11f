"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useEffect, useState } from "react"
import { MONTH_TRANSITION, ACCESSIBILITY } from "@/lib/animations/config"

interface MonthTransitionProps {
  children: React.ReactNode
  monthKey: string // 用于标识月份的唯一键，如 "2025-06"
  direction?: "forward" | "backward" | "none"
  className?: string
}

export default function MonthTransition({ 
  children, 
  monthKey, 
  direction = "none",
  className = ""
}: MonthTransitionProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  // 检测用户的动画偏好
  useEffect(() => {
    if (typeof window !== "undefined") {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)")
      setPrefersReducedMotion(mediaQuery.matches)
      
      const handleChange = (e: MediaQueryListEvent) => {
        setPrefersReducedMotion(e.matches)
      }
      
      mediaQuery.addEventListener("change", handleChange)
      return () => mediaQuery.removeEventListener("change", handleChange)
    }
  }, [])

  // 如果用户偏好减少动画，使用简化版本
  if (prefersReducedMotion && ACCESSIBILITY.respectReducedMotion) {
    return (
      <div className={className}>
        {children}
      </div>
    )
  }

  // 根据方向选择动画变体
  const getVariants = () => {
    if (direction === "none") {
      return {
        initial: { opacity: 1 },
        animate: { opacity: 1 },
        exit: { opacity: 1 }
      }
    }
    
    return direction === "forward" 
      ? MONTH_TRANSITION.forward 
      : MONTH_TRANSITION.backward
  }

  const variants = getVariants()

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <AnimatePresence mode="wait" initial={false}>
        <motion.div
          key={monthKey}
          initial={variants.initial}
          animate={variants.animate}
          exit={variants.exit}
          transition={{
            duration: MONTH_TRANSITION.duration,
            ease: MONTH_TRANSITION.ease
          }}
          className="w-full"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

// 月份导航按钮的微交互
export function MonthNavButton({ 
  children, 
  onClick, 
  disabled = false,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
  [key: string]: any
}) {
  return (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={className}
      whileHover={disabled ? {} : { 
        scale: 1.05,
        transition: { duration: 0.2 }
      }}
      whileTap={disabled ? {} : { 
        scale: 0.95,
        transition: { duration: 0.1 }
      }}
      {...props}
    >
      {children}
    </motion.button>
  )
}

// 月份标题的动画
export function MonthTitle({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.3, 
        delay: 0.1,
        ease: "easeOut"
      }}
    >
      {children}
    </motion.div>
  )
}
