-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建分类表
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  color TEXT,
  priority INTEGER DEFAULT 999,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件表
CREATE TABLE IF NOT EXISTS events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  start_date DATE NOT NULL,
  lunar_date TEXT,
  category_id UUID NOT NULL REFERENCES categories(id),
  image_url TEXT,
  image_alt_text TEXT,
  meta_title TEXT,
  meta_description TEXT,
  url_slug TEXT NOT NULL UNIQUE,
  h1_title TEXT,
  ai_summary TEXT,
  ai_faq JSONB,
  is_recurring BOOLEAN DEFAULT false,
  priority INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- 添加索引以提高查询性能
  CONSTRAINT events_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- 创建事件-标签关联表
CREATE TABLE IF NOT EXISTS event_tags (
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
  PRIMARY KEY (event_id, tag_id)
);

-- 创建索引
CREATE INDEX idx_events_start_date ON events(start_date);
CREATE INDEX idx_events_category_id ON events(category_id);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_url_slug ON events(url_slug);
CREATE INDEX idx_events_name_trgm ON events USING GIN (name gin_trgm_ops);
CREATE INDEX idx_events_ai_summary_trgm ON events USING GIN (ai_summary gin_trgm_ops);

-- 创建用户配置文件表
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE,
  display_name TEXT,
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'editor', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建按分类获取事件的函数
CREATE OR REPLACE FUNCTION get_events_by_category(category_slug TEXT)
RETURNS SETOF events AS $$
BEGIN
  RETURN QUERY
  SELECT e.*
  FROM events e
  JOIN categories c ON e.category_id = c.id
  WHERE c.slug = category_slug
  AND e.status = 'published'
  ORDER BY e.priority DESC, e.start_date;
END;
$$ LANGUAGE plpgsql;

-- 创建搜索事件的函数
CREATE OR REPLACE FUNCTION search_events(query_text TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  start_date DATE,
  url_slug TEXT,
  category_id UUID,
  category_name TEXT,
  rank REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id,
    e.name,
    e.start_date,
    e.url_slug,
    e.category_id,
    c.name AS category_name,
    ts_rank_cd(
      setweight(to_tsvector('simple', e.name), 'A') ||
      setweight(to_tsvector('simple', COALESCE(e.ai_summary, '')), 'B'),
      plainto_tsquery('simple', query_text)
    ) +
    similarity(e.name, query_text) * 2 +
    similarity(COALESCE(e.ai_summary, ''), query_text) AS rank
  FROM events e
  JOIN categories c ON e.category_id = c.id
  WHERE 
    e.status = 'published' AND
    (
      e.name ILIKE '%' || query_text || '%' OR
      COALESCE(e.ai_summary, '') ILIKE '%' || query_text || '%' OR
      EXISTS (
        SELECT 1 FROM event_tags et
        JOIN tags t ON et.tag_id = t.id
        WHERE et.event_id = e.id AND t.name ILIKE '%' || query_text || '%'
      )
    )
  ORDER BY rank DESC, e.priority DESC
  LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- 设置行级安全策略
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 允许所有人读取已发布的事件
CREATE POLICY "允许所有人读取已发布的事件"
ON events FOR SELECT
USING (status = 'published');

-- 允许所有人读取分类
CREATE POLICY "允许所有人读取分类"
ON categories FOR SELECT
USING (true);

-- 允许所有人读取标签
CREATE POLICY "允许所有人读取标签"
ON tags FOR SELECT
USING (true);

-- 允许所有人读取事件标签关联
CREATE POLICY "允许所有人读取事件标签关联"
ON event_tags FOR SELECT
USING (true);

-- 只允许管理员和编辑创建/更新事件
CREATE POLICY "只允许管理员和编辑创建事件"
ON events FOR INSERT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('admin', 'editor')
  )
);

CREATE POLICY "只允许管理员和编辑更新事件"
ON events FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('admin', 'editor')
  )
);

-- 只允许管理员删除事件
CREATE POLICY "只允许管理员删除事件"
ON events FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- 创建触发器函数，自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为所有表添加更新触发器
CREATE TRIGGER update_categories_updated_at
BEFORE UPDATE ON categories
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tags_updated_at
BEFORE UPDATE ON tags
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_events_updated_at
BEFORE UPDATE ON events
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
