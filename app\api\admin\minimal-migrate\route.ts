import { NextResponse } from "next/server"
import { runMinimalMigration } from "@/lib/db/minimal-seed"
import { createClient } from "@/lib/supabase/server"

// 此API路由用于手动触发最小数据迁移
export async function POST(request: Request) {
  try {
    // 验证管理员权限（简化版，实际应用中应该更严格）
    const supabase = createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    // 运行迁移
    const result = await runMinimalMigration()

    if (result.success) {
      return NextResponse.json({ message: "最小数据迁移完成" })
    } else {
      return NextResponse.json({ error: "迁移失败", details: result.error }, { status: 500 })
    }
  } catch (error) {
    console.error("迁移错误:", error)
    return NextResponse.json({ error: "迁移失败" }, { status: 500 })
  }
}
