"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { motion, MotionConfig } from "framer-motion"
import { DURATIONS, EASINGS, ACCESSIBILITY } from "@/lib/animations/config"

interface AnimationContextType {
  reducedMotion: boolean
  animationSpeed: number
  setAnimationSpeed: (speed: number) => void
}

const AnimationContext = createContext<AnimationContextType>({
  reducedMotion: false,
  animationSpeed: 1,
  setAnimationSpeed: () => {}
})

export const useAnimation = () => useContext(AnimationContext)

interface AnimationProviderProps {
  children: React.ReactNode
}

export function AnimationProvider({ children }: AnimationProviderProps) {
  const [reducedMotion, setReducedMotion] = useState(false)
  const [animationSpeed, setAnimationSpeed] = useState(1)

  // 检测用户的动画偏好
  useEffect(() => {
    if (typeof window !== "undefined") {
      const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)")
      setReducedMotion(mediaQuery.matches)
      
      const handleChange = (e: MediaQueryListEvent) => {
        setReducedMotion(e.matches)
      }
      
      mediaQuery.addEventListener("change", handleChange)
      return () => mediaQuery.removeEventListener("change", handleChange)
    }
  }, [])

  // 根据用户偏好调整动画配置
  const motionConfig = {
    transition: reducedMotion && ACCESSIBILITY.respectReducedMotion
      ? ACCESSIBILITY.reducedMotionFallback
      : {
          duration: DURATIONS.normal / 1000 / animationSpeed,
          ease: EASINGS.standard
        }
  }

  return (
    <AnimationContext.Provider value={{
      reducedMotion,
      animationSpeed,
      setAnimationSpeed
    }}>
      <MotionConfig {...motionConfig}>
        {children}
      </MotionConfig>
    </AnimationContext.Provider>
  )
}

// 响应式动画钩子
export function useResponsiveAnimation() {
  const { reducedMotion } = useAnimation()
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768)
      }
      
      checkMobile()
      window.addEventListener("resize", checkMobile)
      return () => window.removeEventListener("resize", checkMobile)
    }
  }, [])

  return {
    shouldAnimate: !reducedMotion,
    isMobile,
    duration: isMobile ? DURATIONS.fast : DURATIONS.normal,
    ease: isMobile ? EASINGS.accelerate : EASINGS.standard
  }
}

// 性能优化的动画组件
export function OptimizedMotion({
  children,
  className = "",
  animate = true,
  ...motionProps
}: {
  children: React.ReactNode
  className?: string
  animate?: boolean
  [key: string]: any
}) {
  const { reducedMotion } = useAnimation()
  
  if (reducedMotion || !animate) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      className={className}
      {...motionProps}
    >
      {children}
    </motion.div>
  )
}

// 智能动画组件 - 根据内容类型自动选择动画
export function SmartAnimation({
  children,
  type = "fadeIn",
  delay = 0,
  className = ""
}: {
  children: React.ReactNode
  type?: "fadeIn" | "slideUp" | "scale" | "bounce"
  delay?: number
  className?: string
}) {
  const { shouldAnimate, duration, ease } = useResponsiveAnimation()

  if (!shouldAnimate) {
    return <div className={className}>{children}</div>
  }

  const variants = {
    fadeIn: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    },
    slideUp: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 }
    },
    scale: {
      initial: { opacity: 0, scale: 0.9 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.9 }
    },
    bounce: {
      initial: { opacity: 0, scale: 0.3 },
      animate: { 
        opacity: 1, 
        scale: 1,
        transition: {
          type: "spring",
          damping: 10,
          stiffness: 100
        }
      },
      exit: { opacity: 0, scale: 0.3 }
    }
  }

  return (
    <motion.div
      className={className}
      variants={variants[type]}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{
        duration: duration / 1000,
        ease,
        delay
      }}
    >
      {children}
    </motion.div>
  )
}

// 动画调试工具（仅开发环境）
export function AnimationDebugger() {
  const { animationSpeed, setAnimationSpeed, reducedMotion } = useAnimation()

  if (process.env.NODE_ENV !== "development") {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs z-50">
      <div className="space-y-2">
        <div>动画状态: {reducedMotion ? "简化" : "正常"}</div>
        <div className="flex items-center gap-2">
          <label>速度:</label>
          <input
            type="range"
            min="0.1"
            max="2"
            step="0.1"
            value={animationSpeed}
            onChange={(e) => setAnimationSpeed(Number(e.target.value))}
            className="w-20"
          />
          <span>{animationSpeed}x</span>
        </div>
      </div>
    </div>
  )
}

// 页面加载动画
export function PageLoadAnimation({
  children,
  className = ""
}: {
  children: React.ReactNode
  className?: string
}) {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])

  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 10 }}
      animate={isLoaded ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
      transition={{
        duration: 0.4,
        ease: "easeOut"
      }}
    >
      {children}
    </motion.div>
  )
}

// 滚动触发动画
export function ScrollAnimation({
  children,
  className = "",
  threshold = 0.1
}: {
  children: React.ReactNode
  className?: string
  threshold?: number
}) {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: threshold }}
      transition={{
        duration: 0.6,
        ease: "easeOut"
      }}
    >
      {children}
    </motion.div>
  )
}
