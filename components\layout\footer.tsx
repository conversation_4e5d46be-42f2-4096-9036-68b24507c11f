import Link from "next/link"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="border-t py-8 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">事件日历</h3>
            <p className="text-sm text-muted-foreground">
              查看中国传统节日、公历节日、农历节日、国际节日等重要事件的日期、历史背景和相关信息。
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/calendar" className="text-sm text-muted-foreground hover:text-foreground">
                  日历
                </Link>
              </li>
              <li>
                <Link href="/categories" className="text-sm text-muted-foreground hover:text-foreground">
                  分类
                </Link>
              </li>
              <li>
                <Link href="/tags" className="text-sm text-muted-foreground hover:text-foreground">
                  标签
                </Link>
              </li>
              <li>
                <Link href="/search" className="text-sm text-muted-foreground hover:text-foreground">
                  搜索
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">关于</h3>
            <p className="text-sm text-muted-foreground">
              事件日历是一个提供各类节日和重要事件信息的平台，旨在帮助用户了解传统文化和重要日期。
            </p>
          </div>
        </div>
        <div className="mt-8 pt-4 border-t text-center text-sm text-muted-foreground">
          &copy; {currentYear} 事件日历. 保留所有权利.
        </div>
      </div>
    </footer>
  )
}
