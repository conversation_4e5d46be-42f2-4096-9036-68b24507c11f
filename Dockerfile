# 使用官方 Node.js 18 Alpine 镜像
FROM node:18-alpine AS base

# 安装依赖阶段
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 复制 package 文件
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# 构建阶段
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# 修改 next.config.mjs 以支持服务器部署
RUN sed -i 's/output: '\''export'\''/\/\/ output: '\''export'\''/g' next.config.mjs

# 构建应用
RUN npm run build

# 运行阶段
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 安装 PM2
RUN npm install -g pm2

# 创建 PM2 配置文件
RUN echo '{\
  "apps": [{\
    "name": "calendar-app",\
    "script": "server.js",\
    "instances": "max",\
    "exec_mode": "cluster",\
    "env": {\
      "NODE_ENV": "production",\
      "PORT": "3000"\
    },\
    "error_file": "/app/logs/err.log",\
    "out_file": "/app/logs/out.log",\
    "log_file": "/app/logs/combined.log",\
    "time": true\
  }]\
}' > ecosystem.config.json

# 创建日志目录
RUN mkdir -p /app/logs && chown -R nextjs:nodejs /app/logs

# 创建上传目录
RUN mkdir -p /app/uploads && chown -R nextjs:nodejs /app/uploads

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# 创建健康检查脚本
RUN echo 'const http = require("http");\
const options = {\
  host: "localhost",\
  port: 3000,\
  path: "/api/health",\
  timeout: 2000\
};\
const request = http.request(options, (res) => {\
  console.log(`STATUS: ${res.statusCode}`);\
  if (res.statusCode == 200) {\
    process.exit(0);\
  } else {\
    process.exit(1);\
  }\
});\
request.on("error", function(err) {\
  console.log("ERROR");\
  process.exit(1);\
});\
request.end();' > healthcheck.js

# 启动应用
CMD ["pm2-runtime", "start", "ecosystem.config.json"]
