import type { Metadata } from "next"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getCategories } from "@/lib/api/events"
import { getCategoryColor } from "@/lib/utils"

export const metadata: Metadata = {
  title: "事件分类 | 传统节日、公历节日、农历节日",
  description: "浏览所有事件分类，包括传统节日、公历节日、农历节日、国际节日等。",
}

export default async function CategoriesPage() {
  const categories = await getCategories()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">事件分类</h1>
      <p className="text-muted-foreground mb-8">浏览所有事件分类，点击任意分类查看相关事件。</p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <Link key={category.id} href={`/category/${category.slug}`}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <Badge style={{ backgroundColor: getCategoryColor(category.name) }} className="text-white">
                    {category.name}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  查看所有{category.name}类型的事件，包括日期、详情和相关信息。
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}
