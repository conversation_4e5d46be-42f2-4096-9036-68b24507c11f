{"apps": [{"name": "calendar-app", "script": "npm", "args": "start", "cwd": "/www/wwwroot/calendar", "instances": "max", "exec_mode": "cluster", "env": {"NODE_ENV": "production", "PORT": "3000"}, "env_production": {"NODE_ENV": "production", "PORT": "3000"}, "error_file": "/www/wwwroot/calendar/logs/err.log", "out_file": "/www/wwwroot/calendar/logs/out.log", "log_file": "/www/wwwroot/calendar/logs/combined.log", "time": true, "autorestart": true, "max_restarts": 10, "min_uptime": "10s", "max_memory_restart": "1G", "watch": false, "ignore_watch": ["node_modules", "logs", ".next"], "source_map_support": true, "instance_var": "INSTANCE_ID", "merge_logs": true, "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "kill_timeout": 5000, "restart_delay": 1000}]}