import { createClient } from "@/lib/supabase/server"
import { notFound } from "next/navigation"
import { cache } from "react"
import type { EventType } from "@/lib/types"

// 使用React的cache函数缓存数据获取结果
export const getCategories = cache(async () => {
  try {
    const supabase = await createClient()
    const { data, error } = await supabase.from("categories").select("*").order("priority", { ascending: true })

    if (error) {
      console.error("Error fetching categories:", error)
      return []
    }

    return data.map((category) => ({
      id: category.id,
      name: category.name as EventType,
      slug: category.slug,
      color: category.color || undefined,
      priority: category.priority || 999,
    }))
  } catch (error) {
    console.error("Unexpected error in getCategories:", error)
    return []
  }
})

export const getEventsByMonth = cache(async (year: number, month: number) => {
  try {
    const supabase = await createClient()

    // 计算月份的开始和结束日期
    const startDate = new Date(year, month - 1, 1).toISOString().split("T")[0]
    const endDate = new Date(year, month, 0).toISOString().split("T")[0]

    const { data, error } = await supabase
      .from("events")
      .select(`
        id,
        name,
        start_date,
        lunar_date,
        url_slug,
        categories:category_id(id, name, slug, color)
      `)
      .gte("start_date", startDate)
      .lte("start_date", endDate)
      .order("start_date", { ascending: true })

    if (error) {
      console.error("Error fetching events:", error)
      return []
    }

    // 转换数据结构以匹配前端需求
    return data.map((event) => ({
      id: event.id,
      name: event.name,
      date: event.start_date,
      type: (event.categories?.name as EventType) || "未分类",
      lunarDate: event.lunar_date || undefined,
      slug: event.url_slug,
    }))
  } catch (error) {
    console.error("Unexpected error in getEventsByMonth:", error)
    return []
  }
})

export const getEventBySlug = cache(async (slug: string) => {
  try {
    const supabase = await createClient()

    // 首先尝试通过url_slug查找
    let { data, error } = await supabase
      .from("events")
      .select(`
        id,
        name,
        seo_title,
        h1_content,
        meta_description,
        page_content,
        start_date,
        lunar_date,
        url_slug,
        faq_schema,
        faq,
        categories:category_id(id, name, slug, color)
      `)
      .eq("url_slug", slug)
      .single()

    // 如果通过url_slug找不到，尝试通过id查找
    if (error && error.code === 'PGRST116') {
      console.log(`Event not found by url_slug: ${slug}, trying by id...`)
      const { data: dataById, error: errorById } = await supabase
        .from("events")
        .select(`
          id,
          name,
          seo_title,
          h1_content,
          meta_description,
          page_content,
          start_date,
          lunar_date,
          url_slug,
          faq_schema,
          faq,
          categories:category_id(id, name, slug, color)
        `)
        .eq("id", slug)
        .single()

      data = dataById
      error = errorById
    }

    if (error || !data) {
      console.error("Error fetching event by slug:", error)
      return notFound()
    }

    // 转换为前端需要的格式
    return {
      id: data.id,
      name: data.name,
      seoTitle: data.seo_title || data.name,
      h1: data.h1_content || data.name,
      metaDescription: data.meta_description || "",
      content: data.page_content || "",
      date: data.start_date,
      type: (data.categories?.name as EventType) || "未分类",
      lunarDate: data.lunar_date || undefined,
      slug: data.url_slug,
      faq: data.faq || data.faq_schema || undefined,
      categoryId: data.categories?.id,
      categorySlug: data.categories?.slug,
      categoryName: data.categories?.name,
    }
  } catch (error) {
    console.error("Unexpected error in getEventBySlug:", error)
    return notFound()
  }
})

export const getEventsByCategory = cache(async (categorySlug: string) => {
  try {
    const supabase = await createClient()

    // 先获取分类ID
    const { data: category, error: categoryError } = await supabase
      .from("categories")
      .select("id, name")
      .eq("slug", categorySlug)
      .single()

    if (categoryError || !category) {
      console.error("Error fetching category:", categoryError)
      return []
    }

    // 使用分类ID查询事件
    const { data, error } = await supabase
      .from("events")
      .select(`
        id,
        name,
        seo_title,
        meta_description,
        start_date,
        lunar_date,
        url_slug
      `)
      .eq("category_id", category.id)
      .order("start_date", { ascending: true })

    if (error) {
      console.error("Error fetching events by category:", error)
      return []
    }

    // 转换数据结构以匹配前端需求
    return data.map((event) => ({
      id: event.id,
      name: event.name,
      seoTitle: event.seo_title || event.name,
      metaDescription: event.meta_description || "",
      date: event.start_date,
      type: category.name as EventType,
      lunarDate: event.lunar_date || undefined,
      slug: event.url_slug,
    }))
  } catch (error) {
    console.error("Unexpected error in getEventsByCategory:", error)
    return []
  }
})

// 获取所有标签（这里我们需要创建一个tags表，或者从events中提取）
export const getAllTags = cache(async () => {
  // 这里假设我们有一个tags表
  // 实际情况可能需要调整
  try {
    const supabase = await createClient()
    const { data, error } = await supabase.from("tags").select("*").order("name", { ascending: true })

    if (error) {
      console.error("Error fetching tags:", error)
      return []
    }

    return data
  } catch (error) {
    console.error("Unexpected error in getAllTags:", error)
    return []
  }
})

// 搜索事件函数
export const searchEvents = cache(async (query: string) => {
  try {
    if (!query.trim()) return []

    const supabase = await createClient()

    // 使用简单的ILIKE搜索
    const { data, error } = await supabase
      .from("events")
      .select(`
        id,
        name,
        seo_title,
        meta_description,
        start_date,
        url_slug,
        categories:category_id(name)
      `)
      .or(
        `name.ilike.%${query}%,seo_title.ilike.%${query}%,meta_description.ilike.%${query}%,page_content.ilike.%${query}%`,
      )
      .limit(20)

    if (error) {
      console.error("Error searching events:", error)
      return []
    }

    return data.map((event) => ({
      id: event.id,
      name: event.name,
      seoTitle: event.seo_title || event.name,
      metaDescription: event.meta_description || "",
      date: event.start_date,
      type: (event.categories?.name as EventType) || "未分类",
      slug: event.url_slug,
    }))
  } catch (error) {
    console.error("Unexpected error in searchEvents:", error)
    return []
  }
})
