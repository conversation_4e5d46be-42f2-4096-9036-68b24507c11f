export type EventType = "公历" | "农历" | "重要事件" | "国际节日" | "节气" | "纪念日" | "会展" | "品牌日" | "娱乐"

export interface Event {
  id: string
  name: string
  date: string
  type: EventType
  lunarDate?: string
  slug?: string
  seoTitle?: string
  h1?: string
  metaDescription?: string
  content?: string
  faq?: Array<{
    question: string
    answer: string
  }>
  categoryId?: string
  categorySlug?: string
  categoryName?: string
  description?: string
  tags?: string[]
  location?: string
}

// 事件类型的优先级排序（数字越小优先级越高）
export const EVENT_TYPE_PRIORITY: Record<EventType, number> = {
  公历: 1,
  农历: 2,
  重要事件: 3,
  国际节日: 4,
  节气: 5,
  纪念日: 6,
  会展: 7,
  品牌日: 8,
  娱乐: 9,
}

export type EventStatus = "active" | "cancelled" | "postponed" | "completed"

// 默认显示的分类
export const DEFAULT_CATEGORIES: EventType[] = ["公历", "农历", "国际节日", "节气"]
