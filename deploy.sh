#!/bin/bash

# 日历应用部署脚本
# 使用方法: ./deploy.sh [环境] [域名]
# 示例: ./deploy.sh production calendar.example.com

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
DOMAIN=${2:-localhost}

log_info "开始部署日历应用..."
log_info "环境: $ENVIRONMENT"
log_info "域名: $DOMAIN"

# 检查必要工具
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p nginx/logs
    mkdir -p nginx/ssl
    mkdir -p uploads
    mkdir -p postgres_data
    mkdir -p redis_data
    
    log_success "目录创建完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f .env.production ]; then
        log_error ".env.production 文件不存在，请先创建配置文件"
        exit 1
    fi
    
    # 替换域名
    sed -i "s/your-domain.com/$DOMAIN/g" nginx/sites-available/calendar.conf
    sed -i "s/your-domain.com/$DOMAIN/g" .env.production
    
    log_success "环境变量配置完成"
}

# 生成 SSL 证书
setup_ssl() {
    log_info "设置 SSL 证书..."
    
    if [ "$DOMAIN" != "localhost" ]; then
        log_info "为域名 $DOMAIN 申请 Let's Encrypt 证书..."
        
        # 创建临时 nginx 配置用于验证
        docker run --rm -v $(pwd)/nginx/ssl:/etc/letsencrypt \
            -v $(pwd)/nginx/logs:/var/log/letsencrypt \
            -p 80:80 \
            certbot/certbot certonly --standalone \
            --email admin@$DOMAIN \
            --agree-tos \
            --no-eff-email \
            -d $DOMAIN -d www.$DOMAIN
        
        # 复制证书到 nginx 目录
        cp nginx/ssl/live/$DOMAIN/fullchain.pem nginx/ssl/
        cp nginx/ssl/live/$DOMAIN/privkey.pem nginx/ssl/
    else
        log_warning "本地部署，生成自签名证书..."
        
        # 生成自签名证书
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/privkey.pem \
            -out nginx/ssl/fullchain.pem \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=$DOMAIN"
    fi
    
    log_success "SSL 证书设置完成"
}

# 构建和启动服务
deploy_services() {
    log_info "构建和启动服务..."
    
    # 停止现有服务
    docker-compose down --remove-orphans
    
    # 构建镜像
    docker-compose build --no-cache
    
    # 启动服务
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库
    log_info "等待数据库启动..."
    sleep 30
    
    # 等待应用
    log_info "等待应用启动..."
    for i in {1..30}; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            log_success "应用已就绪"
            break
        fi
        sleep 5
    done
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 这里可以添加数据库迁移逻辑
    # docker-compose exec app npm run migrate
    
    log_success "数据库迁移完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查各个服务状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "部分服务未正常启动"
        docker-compose logs
        exit 1
    fi
    
    # 检查应用响应
    if ! curl -f http://localhost:3000/api/health &> /dev/null; then
        log_error "应用健康检查失败"
        exit 1
    fi
    
    log_success "健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 部署完成！"
    echo ""
    echo "📋 部署信息:"
    echo "  - 应用地址: https://$DOMAIN"
    echo "  - 健康检查: https://$DOMAIN/health"
    echo "  - 数据库: PostgreSQL (端口 5432)"
    echo "  - 缓存: Redis (端口 6379)"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 停止服务: docker-compose down"
    echo "  - 更新应用: ./deploy.sh $ENVIRONMENT $DOMAIN"
    echo ""
}

# 主执行流程
main() {
    check_requirements
    create_directories
    setup_environment
    setup_ssl
    deploy_services
    wait_for_services
    run_migrations
    health_check
    show_deployment_info
}

# 执行部署
main
