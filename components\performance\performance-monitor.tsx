'use client'

/**
 * 性能监控组件
 * 显示实时性能指标（仅开发环境）
 */

import { useState, useEffect } from 'react'
import { BundleAnalyzer } from '@/lib/performance/bundle-analyzer'

interface PerformanceMetrics {
  fps: number
  memory: {
    used: number
    total: number
  } | null
  loadTime: number
  resourceCount: number
  criticalResources: Array<{
    name: string
    size: number
    loadTime: number
    type: string
  }>
}

export default function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const updateMetrics = () => {
      const now = performance.now()
      frameCount++

      // 每秒更新一次指标
      if (now - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (now - lastTime))
        frameCount = 0
        lastTime = now

        const bundleInfo = BundleAnalyzer.getBundleInfo()
        const criticalResources = BundleAnalyzer.analyzeCriticalResources()

        setMetrics({
          fps,
          memory: bundleInfo?.memory ? {
            used: Math.round(bundleInfo.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(bundleInfo.memory.totalJSHeapSize / 1024 / 1024)
          } : null,
          loadTime: bundleInfo?.navigation ?
            Math.round(bundleInfo.navigation.loadEventEnd - bundleInfo.navigation.navigationStart) : 0,
          resourceCount: bundleInfo?.resources.length || 0,
          criticalResources: criticalResources.slice(0, 5) // 只显示前5个
        })
      }

      animationId = requestAnimationFrame(updateMetrics)
    }

    updateMetrics()

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [])

  if (!metrics) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-black text-white px-3 py-2 rounded-lg text-sm font-mono shadow-lg hover:bg-gray-800 transition-colors"
      >
        {isVisible ? '隐藏' : '性能'} ({metrics.fps} FPS)
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-black text-white p-4 rounded-lg shadow-xl font-mono text-xs w-80 max-h-96 overflow-y-auto">
          <div className="space-y-3">
            {/* FPS */}
            <div className="flex justify-between">
              <span>FPS:</span>
              <span className={metrics.fps < 30 ? 'text-red-400' : metrics.fps < 50 ? 'text-yellow-400' : 'text-green-400'}>
                {metrics.fps}
              </span>
            </div>

            {/* 内存使用 */}
            {metrics.memory && (
              <div className="flex justify-between">
                <span>内存:</span>
                <span className={metrics.memory.used > 50 ? 'text-red-400' : 'text-green-400'}>
                  {metrics.memory.used}MB / {metrics.memory.total}MB
                </span>
              </div>
            )}

            {/* 加载时间 */}
            <div className="flex justify-between">
              <span>加载时间:</span>
              <span className={metrics.loadTime > 3000 ? 'text-red-400' : metrics.loadTime > 1000 ? 'text-yellow-400' : 'text-green-400'}>
                {metrics.loadTime}ms
              </span>
            </div>

            {/* 资源数量 */}
            <div className="flex justify-between">
              <span>资源数量:</span>
              <span>{metrics.resourceCount}</span>
            </div>

            {/* 关键资源 */}
            {metrics.criticalResources.length > 0 && (
              <div>
                <div className="text-gray-300 mb-2">关键资源:</div>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {metrics.criticalResources.map((resource, index) => (
                    <div key={index} className="text-xs">
                      <div className="flex justify-between">
                        <span className="truncate flex-1 mr-2" title={resource.name}>
                          {resource.name.split('/').pop()}
                        </span>
                        <span className="text-gray-400">
                          {Math.round(resource.size / 1024)}KB
                        </span>
                      </div>
                      <div className="flex justify-between text-gray-500">
                        <span>{resource.type}</span>
                        <span>{Math.round(resource.loadTime)}ms</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="pt-2 border-t border-gray-600 space-y-2">
              <button
                onClick={() => {
                  console.log('Performance metrics:', metrics)
                  console.log('Bundle info:', BundleAnalyzer.getBundleInfo())
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
              >
                导出到控制台
              </button>

              <button
                onClick={() => {
                  if (typeof window !== 'undefined') {
                    const { flushWebVitals } = require('@/lib/performance/web-vitals')
                    flushWebVitals()
                  }
                }}
                className="w-full bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
              >
                刷新 Web Vitals
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
