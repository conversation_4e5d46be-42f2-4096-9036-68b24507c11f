# 🎛️ 宝塔面板部署指南

## 📋 **部署方案概述**

基于宝塔面板的部署方案，结合 Docker 容器化和传统服务器管理的优势。

## 🏗️ **架构设计**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   宝塔 Nginx    │    │   Node.js App    │    │  PostgreSQL     │
│   (反向代理)     │────│   (PM2 管理)     │────│   (宝塔管理)     │
│   SSL 管理      │    │   端口: 3000     │    │   端口: 5432     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 **部署步骤**

### **第一步：宝塔面板基础配置**

#### 1.1 安装必要软件
在宝塔面板 → 软件商店 → 安装以下软件：
- **Nginx** 1.22+
- **PostgreSQL** 15.x （重要：不要选择 MySQL）
- **Node.js 版本管理器** 
- **PM2 管理器**
- **Redis** 7.x

#### 1.2 Node.js 环境配置
```bash
# 在宝塔终端中执行
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
npm install -g pm2
```

### **第二步：数据库配置**

#### 2.1 创建 PostgreSQL 数据库
在宝塔面板 → 数据库 → PostgreSQL：
- 数据库名：`calendar_db`
- 用户名：`calendar_user`
- 密码：`设置强密码`

#### 2.2 配置数据库连接
```bash
# 编辑 PostgreSQL 配置允许本地连接
sudo -u postgres psql
ALTER USER calendar_user WITH SUPERUSER;
\q
```

#### 2.3 导入数据库结构
```bash
# 在项目目录执行
sudo -u postgres psql -d calendar_db -f scripts/init-database.sql
```

### **第三步：项目部署**

#### 3.1 上传项目文件
在宝塔面板 → 文件 → 创建目录：`/www/wwwroot/calendar`
上传所有项目文件到此目录

#### 3.2 安装依赖
```bash
cd /www/wwwroot/calendar
npm install --production
```

#### 3.3 配置环境变量
```bash
# 创建生产环境配置
cp .env.production .env.local

# 编辑配置文件
vim .env.local
```

#### 3.4 构建项目
```bash
# 设置环境变量
export NODE_ENV=production

# 构建项目
npm run build
```

### **第四步：PM2 进程管理**

#### 4.1 创建 PM2 配置文件
```json
{
  "apps": [{
    "name": "calendar-app",
    "script": "npm",
    "args": "start",
    "cwd": "/www/wwwroot/calendar",
    "instances": "max",
    "exec_mode": "cluster",
    "env": {
      "NODE_ENV": "production",
      "PORT": "3000"
    },
    "error_file": "/www/wwwroot/calendar/logs/err.log",
    "out_file": "/www/wwwroot/calendar/logs/out.log",
    "log_file": "/www/wwwroot/calendar/logs/combined.log",
    "time": true,
    "autorestart": true,
    "max_restarts": 10,
    "min_uptime": "10s"
  }]
}
```

#### 4.2 启动应用
```bash
# 创建日志目录
mkdir -p /www/wwwroot/calendar/logs

# 启动应用
pm2 start ecosystem.config.json

# 保存 PM2 配置
pm2 save
pm2 startup
```

### **第五步：Nginx 反向代理配置**

#### 5.1 在宝塔面板创建网站
- 域名：`yourdomain.com`
- 根目录：`/www/wwwroot/calendar/public`
- PHP版本：纯静态

#### 5.2 配置反向代理
在宝塔面板 → 网站 → 设置 → 反向代理：

**代理名称**: calendar-app
**目标URL**: http://127.0.0.1:3000
**发送域名**: $host
**内容替换**: 留空

#### 5.3 自定义 Nginx 配置
在网站设置 → 配置文件中添加：

```nginx
# 在 server 块中添加以下配置

# 静态文件缓存
location /_next/static {
    proxy_pass http://127.0.0.1:3000;
    proxy_cache_valid 200 1d;
    add_header Cache-Control "public, max-age=31536000, immutable";
}

# 图片文件缓存
location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
    proxy_pass http://127.0.0.1:3000;
    add_header Cache-Control "public, max-age=86400";
}

# API 路由
location /api/ {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}

# 主应用
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}

# 健康检查
location /health {
    proxy_pass http://127.0.0.1:3000/api/health;
    access_log off;
}
```

### **第六步：安全和优化配置**

#### 6.1 防火墙配置
在宝塔面板 → 安全：
- 开放端口：22, 80, 443, 8888
- 关闭端口：3000, 5432 (仅内部访问)

#### 6.2 定时任务配置
在宝塔面板 → 计划任务：

**数据库备份**:
```bash
# 每天凌晨2点备份数据库
0 2 * * * pg_dump -h localhost -U calendar_user calendar_db > /www/backup/calendar_db_$(date +\%Y\%m\%d).sql
```

**日志清理**:
```bash
# 每周清理7天前的日志
0 0 * * 0 find /www/wwwroot/calendar/logs -name "*.log" -mtime +7 -delete
```

#### 6.3 监控配置
在宝塔面板 → 监控：
- 启用系统监控
- 设置 CPU、内存、磁盘告警阈值
- 配置邮件通知

## 🔧 **管理和维护**

### **应用管理**
```bash
# 查看应用状态
pm2 status

# 重启应用
pm2 restart calendar-app

# 查看日志
pm2 logs calendar-app

# 监控应用
pm2 monit
```

### **数据库管理**
- 使用宝塔面板 → 数据库 → PostgreSQL 管理
- 或使用 pgAdmin 等工具连接

### **性能监控**
- 宝塔面板 → 监控 查看系统状态
- 访问 `/api/health` 检查应用健康状态

## 🚨 **故障排除**

### **常见问题**

1. **应用无法启动**
   ```bash
   pm2 logs calendar-app --lines 50
   ```

2. **数据库连接失败**
   ```bash
   sudo -u postgres psql -d calendar_db -c "SELECT 1;"
   ```

3. **Nginx 配置错误**
   - 在宝塔面板检查 Nginx 错误日志
   - 测试配置：`nginx -t`

4. **端口冲突**
   ```bash
   netstat -tulpn | grep :3000
   ```

## 📞 **技术支持**

如果遇到问题，请提供：
- 宝塔面板版本
- 错误日志内容
- 系统资源使用情况
- 具体错误现象描述

---

**部署完成后，您的应用将在 `http://yourdomain.com` 上运行！**
