import { revalidatePath } from "next/cache"
import { NextResponse } from "next/server"
import { headers } from "next/headers"
import crypto from "crypto"

// 验证Supabase Webhook签名
function verifySignature(body: string, signature: string): boolean {
  const webhookSecret = process.env.SUPABASE_WEBHOOK_SECRET

  if (!webhookSecret) {
    console.warn("SUPABASE_WEBHOOK_SECRET not set")
    return false
  }

  // 使用HMAC-SHA256算法验证签名
  const hmac = crypto.createHmac("sha256", webhookSecret)
  const digest = hmac.update(body).digest("hex")

  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest))
}

export async function POST(request: Request) {
  try {
    const headersList = headers()
    const signature = headersList.get("x-supabase-signature")

    if (!signature) {
      return NextResponse.json({ error: "Missing signature" }, { status: 401 })
    }

    const body = await request.text()

    // 验证签名
    if (!verifySignature(body, signature)) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    const payload = JSON.parse(body)

    // 根据不同的表和操作类型重新验证不同的路径
    const { table, type, record } = payload

    if (table === "events") {
      // 重新验证事件详情页
      if (record?.url_slug) {
        revalidatePath(`/event/${record.url_slug}`)
      }

      // 重新验证日历页
      if (record?.start_date) {
        const date = new Date(record.start_date)
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        revalidatePath(`/calendar/${year}/${month.toString().padStart(2, "0")}`)
      }

      // 重新验证分类页
      if (record?.category_id) {
        revalidatePath(`/category/[slug]`)
      }

      // 重新验证首页
      revalidatePath("/")
    } else if (table === "categories") {
      // 重新验证分类相关页面
      revalidatePath("/category/[slug]")
      revalidatePath("/")
    } else if (table === "tags" || table === "event_tags") {
      // 重新验证标签相关页面
      revalidatePath("/tag/[slug]")
    }

    return NextResponse.json({ revalidated: true })
  } catch (error) {
    console.error("Revalidation error:", error)
    return NextResponse.json({ error: "Revalidation failed" }, { status: 500 })
  }
}
