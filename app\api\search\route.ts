import { NextResponse } from "next/server"
import { searchEvents } from "@/lib/api/events"

// 简单的内存缓存
const CACHE_MAX_AGE = 5 * 60 * 1000 // 5分钟
const cache = new Map<string, { data: any; timestamp: number }>()

// 清理过期缓存
function cleanupCache() {
  const now = Date.now()
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_MAX_AGE) {
      cache.delete(key)
    }
  }
}

// 定期清理缓存
setInterval(cleanupCache, 60 * 1000) // 每分钟清理一次

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q")

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ results: [] })
    }

    // 检查缓存
    const cacheKey = query.trim().toLowerCase()
    const cachedResult = cache.get(cacheKey)

    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_MAX_AGE) {
      // 返回缓存的结果
      return NextResponse.json(cachedResult.data, {
        headers: {
          "Cache-Control": "public, max-age=300",
          "X-Cache": "HIT",
        },
      })
    }

    // 搜索事件
    const results = await searchEvents(query)

    // 更新缓存
    const responseData = { results }
    cache.set(cacheKey, {
      data: responseData,
      timestamp: Date.now(),
    })

    return NextResponse.json(responseData, {
      headers: {
        "Cache-Control": "public, max-age=300",
        "X-Cache": "MISS",
      },
    })
  } catch (error) {
    console.error("搜索错误:", error)
    return NextResponse.json({ error: "搜索失败", details: String(error) }, { status: 500 })
  }
}
