# Cloudflare Workers 配置
name = "marketing-calendar"
main = "workers/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = ["workers", "app", "components", "lib"]

# 环境变量
[vars]
NODE_ENV = "production"

# 开发环境
[env.development]
name = "marketing-calendar-dev"
workers_dev = true

[env.development.vars]
NODE_ENV = "development"

# 生产环境
[env.production]
name = "marketing-calendar-prod"
workers_dev = false

[env.production.vars]
NODE_ENV = "production"

# KV 存储绑定 (需要手动创建)
# [[env.production.kv_namespaces]]
# binding = "CACHE_KV"
# id = "your-kv-namespace-id"

# R2 存储绑定 (可选)
# [[env.production.r2_buckets]]
# binding = "ASSETS_BUCKET"
# bucket_name = "marketing-calendar-assets"

# 路由配置 (需要配置域名)
# [[env.production.routes]]
# pattern = "your-domain.com/*"
# zone_name = "your-domain.com"
