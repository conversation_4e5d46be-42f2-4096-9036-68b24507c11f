#!/bin/bash

# 宝塔面板部署脚本
# 使用方法: ./bt-deploy.sh [域名]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DOMAIN=${1:-localhost}
PROJECT_PATH="/www/wwwroot/calendar"

log_info "🎛️ 开始宝塔面板部署..."
log_info "域名: $DOMAIN"
log_info "项目路径: $PROJECT_PATH"

# 检查是否在宝塔环境
check_bt_environment() {
    log_info "检查宝塔环境..."
    
    if [ ! -d "/www/server" ]; then
        log_error "未检测到宝塔面板，请先安装宝塔面板"
        exit 1
    fi
    
    if [ ! -f "/etc/init.d/bt" ]; then
        log_warning "宝塔服务未找到，请确认宝塔面板正常运行"
    fi
    
    log_success "宝塔环境检查通过"
}

# 检查必要软件
check_software() {
    log_info "检查必要软件..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请在宝塔面板安装 Node.js 版本管理器"
        exit 1
    fi
    
    # 检查 PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，正在安装..."
        npm install -g pm2
    fi
    
    # 检查 PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL 未安装，请在宝塔面板安装 PostgreSQL"
        exit 1
    fi
    
    log_success "软件检查完成"
}

# 创建项目目录
setup_project_directory() {
    log_info "设置项目目录..."
    
    if [ ! -d "$PROJECT_PATH" ]; then
        mkdir -p "$PROJECT_PATH"
        log_success "项目目录已创建: $PROJECT_PATH"
    else
        log_info "项目目录已存在: $PROJECT_PATH"
    fi
    
    # 创建必要的子目录
    mkdir -p "$PROJECT_PATH/logs"
    mkdir -p "$PROJECT_PATH/uploads"
    mkdir -p "/www/backup"
    
    # 设置权限
    chown -R www:www "$PROJECT_PATH"
    chmod -R 755 "$PROJECT_PATH"
    
    log_success "目录结构设置完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    cd "$PROJECT_PATH"
    
    if [ ! -f ".env.local" ]; then
        if [ -f ".env.bt-panel" ]; then
            cp .env.bt-panel .env.local
        else
            log_error "环境配置文件不存在，请先上传项目文件"
            exit 1
        fi
    fi
    
    # 生成随机密钥
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    NEXTAUTH_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    REVALIDATION_SECRET=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    
    # 替换配置文件中的占位符
    sed -i "s/your_password_here/$POSTGRES_PASSWORD/g" .env.local
    sed -i "s/your_nextauth_secret_here_32_chars_min/$NEXTAUTH_SECRET/g" .env.local
    sed -i "s/your_revalidation_secret_here/$REVALIDATION_SECRET/g" .env.local
    sed -i "s/yourdomain.com/$DOMAIN/g" .env.local
    
    log_success "环境变量配置完成"
    log_info "PostgreSQL 密码: $POSTGRES_PASSWORD"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    cd "$PROJECT_PATH"
    
    if [ ! -f "package.json" ]; then
        log_error "package.json 不存在，请先上传项目文件"
        exit 1
    fi
    
    # 清理可能的缓存
    rm -rf node_modules package-lock.json
    
    # 安装依赖
    npm install --production
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    cd "$PROJECT_PATH"
    
    # 设置环境变量
    export NODE_ENV=production
    
    # 构建项目
    npm run build
    
    log_success "项目构建完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 从环境文件读取数据库密码
    DB_PASSWORD=$(grep "POSTGRES_PASSWORD=" "$PROJECT_PATH/.env.local" | cut -d'=' -f2)
    
    # 创建数据库用户和数据库
    sudo -u postgres psql -c "CREATE USER calendar_user WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
    sudo -u postgres psql -c "CREATE DATABASE calendar_db OWNER calendar_user;" 2>/dev/null || true
    sudo -u postgres psql -c "ALTER USER calendar_user WITH SUPERUSER;" 2>/dev/null || true
    
    # 导入数据库结构
    if [ -f "$PROJECT_PATH/scripts/init-database.sql" ]; then
        sudo -u postgres psql -d calendar_db -f "$PROJECT_PATH/scripts/init-database.sql"
        log_success "数据库初始化完成"
    else
        log_warning "数据库初始化脚本未找到"
    fi
}

# 启动应用
start_application() {
    log_info "启动应用..."
    
    cd "$PROJECT_PATH"
    
    # 停止现有进程
    pm2 delete calendar-app 2>/dev/null || true
    
    # 启动新进程
    if [ -f "ecosystem.config.json" ]; then
        pm2 start ecosystem.config.json
    else
        pm2 start npm --name "calendar-app" -- start
    fi
    
    # 保存 PM2 配置
    pm2 save
    
    # 设置开机自启
    pm2 startup systemd -u root --hp /root
    
    log_success "应用启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    sleep 10
    
    for i in {1..30}; do
        if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "应用健康检查通过"
            return 0
        fi
        sleep 2
    done
    
    log_error "应用健康检查失败"
    pm2 logs calendar-app --lines 20
    return 1
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 宝塔面板部署完成！"
    echo ""
    echo "📋 部署信息:"
    echo "  - 项目路径: $PROJECT_PATH"
    echo "  - 应用地址: http://$DOMAIN"
    echo "  - 健康检查: http://$DOMAIN/api/health"
    echo "  - 应用端口: 3000"
    echo ""
    echo "🔧 管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs calendar-app"
    echo "  - 重启应用: pm2 restart calendar-app"
    echo "  - 停止应用: pm2 stop calendar-app"
    echo ""
    echo "🎛️ 宝塔面板配置:"
    echo "  - 请在宝塔面板创建网站: $DOMAIN"
    echo "  - 配置反向代理到: http://127.0.0.1:3000"
    echo "  - 根目录设置为: $PROJECT_PATH/public"
    echo ""
    echo "📊 应用状态:"
    pm2 status
    echo ""
}

# 主执行流程
main() {
    check_bt_environment
    check_software
    setup_project_directory
    setup_environment
    install_dependencies
    build_project
    setup_database
    start_application
    health_check
    show_deployment_info
}

# 执行部署
main
