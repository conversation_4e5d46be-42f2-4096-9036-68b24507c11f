// 简化版农历转换函数
// 注意：这是一个简化版实现，实际应用中应使用更完整的农历库
export function getSimpleLunarDate(date: Date): string {
  // 农历月份名称
  const lunarMonths = ["正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"]

  // 农历日期名称
  const lunarDays = [
    "初一",
    "初二",
    "初三",
    "初四",
    "初五",
    "初六",
    "初七",
    "初八",
    "初九",
    "初十",
    "十一",
    "十二",
    "十三",
    "十四",
    "十五",
    "十六",
    "十七",
    "十八",
    "十九",
    "二十",
    "廿一",
    "廿二",
    "廿三",
    "廿四",
    "廿五",
    "廿六",
    "廿七",
    "廿八",
    "廿九",
    "三十",
  ]

  // 这里使用简单算法模拟农历日期
  // 实际应用中应使用准确的农历转换库
  const year = date.getFullYear()
  const month = date.getMonth()
  const day = date.getDate()

  // 简单模拟，实际应用需要更准确的算法
  // 这里仅用于演示布局效果
  const lunarMonthIndex = (month + Math.floor(year % 5)) % 12
  const lunarDayIndex = (day - 1) % 30

  return `${lunarMonths[lunarMonthIndex]}月${lunarDays[lunarDayIndex]}`
}

// 获取节气、节日等特殊日期（简化版）
export function getSpecialLunarEvents(date: Date): string | null {
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 简单的节气和传统节日映射
  const specialDates: Record<string, string> = {
    "1-1": "春节",
    "1-15": "元宵节",
    "5-5": "端午节",
    "8-15": "中秋节",
    "9-9": "重阳节",
    "12-30": "除夕",
    // 简化的二十四节气（实际应该使用更准确的计算）
    "2-4": "立春",
    "3-6": "惊蛰",
    "4-5": "清明",
    "5-6": "立夏",
    "6-6": "芒种",
    "7-7": "小暑",
    "8-8": "立秋",
    "9-8": "白露",
    "10-8": "寒露",
    "11-7": "立冬",
    "12-7": "大雪",
  }

  const key = `${month}-${day}`
  return specialDates[key] || null
}
