#!/bin/bash

# 日历应用同步部署脚本
# 使用方法: ./deploy-sync.sh [local|remote]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
SERVER_IP="*************"
SERVER_USER="root"
SERVER_PATH="/www/wwwroot/calendar"
LOCAL_PATH="."
BACKUP_DIR="/www/backup/calendar"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 本地构建和测试
local_build() {
    log_info "🏗️ 本地构建和测试..."
    
    # 安装依赖
    log_info "安装依赖..."
    npm install
    
    # 运行测试（如果有）
    if [ -f "package.json" ] && grep -q "test" package.json; then
        log_info "运行测试..."
        npm test || log_warning "测试失败，继续部署"
    fi
    
    # 构建项目
    log_info "构建项目..."
    npm run build
    
    # 检查构建结果
    if [ -d ".next" ]; then
        log_success "构建成功"
    else
        log_error "构建失败"
        exit 1
    fi
}

# 同步到服务器
sync_to_server() {
    log_info "📤 同步文件到服务器..."
    
    # 创建备份
    log_info "创建服务器备份..."
    ssh $SERVER_USER@$SERVER_IP "
        mkdir -p $BACKUP_DIR
        tar -czf $BACKUP_DIR/calendar_backup_\$(date +%Y%m%d_%H%M%S).tar.gz -C $SERVER_PATH .
        find $BACKUP_DIR -name '*.tar.gz' -mtime +7 -delete
    "
    
    # 排除文件列表
    cat > .rsync-exclude << 'EOF'
node_modules/
.git/
.next/
dist/
logs/
*.log
.env.local
.env.production
uploads/
EOF
    
    # 同步文件
    log_info "同步项目文件..."
    rsync -avz --delete \
        --exclude-from=.rsync-exclude \
        --progress \
        $LOCAL_PATH/ $SERVER_USER@$SERVER_IP:$SERVER_PATH/
    
    # 清理临时文件
    rm .rsync-exclude
    
    log_success "文件同步完成"
}

# 服务器部署
remote_deploy() {
    log_info "🚀 服务器部署..."
    
    ssh $SERVER_USER@$SERVER_IP "
        cd $SERVER_PATH
        
        # 安装依赖
        echo '📦 安装依赖...'
        npm install --production --legacy-peer-deps
        
        # 构建项目
        echo '🏗️ 构建项目...'
        export NODE_ENV=production
        npm run build
        
        # 重启应用
        echo '🔄 重启应用...'
        pm2 restart calendar-app || pm2 start ecosystem.production.json
        
        # 等待启动
        sleep 10
        
        # 健康检查
        echo '🔍 健康检查...'
        curl -f http://localhost:3000/api/health || echo '⚠️ 健康检查失败'
        
        # 显示状态
        pm2 status
        
        echo '✅ 部署完成！'
    "
}

# 回滚功能
rollback() {
    log_warning "🔄 执行回滚..."
    
    ssh $SERVER_USER@$SERVER_IP "
        cd $BACKUP_DIR
        LATEST_BACKUP=\$(ls -t calendar_backup_*.tar.gz | head -1)
        
        if [ -n \"\$LATEST_BACKUP\" ]; then
            echo \"回滚到: \$LATEST_BACKUP\"
            
            # 停止应用
            pm2 stop calendar-app
            
            # 恢复备份
            tar -xzf \$LATEST_BACKUP -C $SERVER_PATH
            
            # 重启应用
            cd $SERVER_PATH
            pm2 restart calendar-app
            
            echo '✅ 回滚完成'
        else
            echo '❌ 没有找到备份文件'
            exit 1
        fi
    "
}

# 监控和日志
monitor() {
    log_info "📊 应用监控..."
    
    ssh $SERVER_USER@$SERVER_IP "
        echo '=== PM2 状态 ==='
        pm2 status
        
        echo -e '\n=== 应用日志 (最近20行) ==='
        pm2 logs calendar-app --lines 20
        
        echo -e '\n=== 系统资源 ==='
        free -h
        df -h | grep -E '(Filesystem|/www)'
        
        echo -e '\n=== 网络连接 ==='
        netstat -tulpn | grep :3000
        
        echo -e '\n=== 健康检查 ==='
        curl -s http://localhost:3000/api/health | jq . || curl -s http://localhost:3000/api/health
    "
}

# 主函数
main() {
    case "$1" in
        "local")
            log_info "🏠 本地构建模式"
            local_build
            ;;
        "remote")
            log_info "🌐 远程部署模式"
            sync_to_server
            remote_deploy
            ;;
        "full")
            log_info "🚀 完整部署流程"
            local_build
            sync_to_server
            remote_deploy
            monitor
            ;;
        "rollback")
            rollback
            ;;
        "monitor")
            monitor
            ;;
        *)
            echo "使用方法: $0 [local|remote|full|rollback|monitor]"
            echo ""
            echo "命令说明:"
            echo "  local    - 本地构建和测试"
            echo "  remote   - 同步并部署到服务器"
            echo "  full     - 完整流程（本地构建 + 远程部署 + 监控）"
            echo "  rollback - 回滚到上一个版本"
            echo "  monitor  - 查看应用状态和日志"
            echo ""
            echo "示例:"
            echo "  $0 full      # 完整部署"
            echo "  $0 monitor   # 查看状态"
            echo "  $0 rollback  # 紧急回滚"
            exit 1
            ;;
    esac
}

main "$@"
