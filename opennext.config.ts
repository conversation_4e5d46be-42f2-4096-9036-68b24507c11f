import type { OpenNextConfig } from '@opennext/aws'

const config: OpenNextConfig = {
  default: {
    override: {
      wrapper: 'cloudflare',
      converter: 'edge',
      // Cloudflare Workers 特定配置
      generateDockerfile: false,
    },
  },
  
  // 函数配置
  functions: {
    // API 路由优化
    api: {
      runtime: 'edge',
      memory: 128,
      timeout: 30,
      patterns: ['api/**'],
    },
    
    // 页面渲染函数
    pages: {
      runtime: 'edge',
      memory: 256,
      timeout: 60,
      patterns: ['**'],
    },
  },
  
  // 中间件配置
  middleware: {
    external: true,
    originResolver: true,
  },
  
  // 静态资源配置
  buildCommand: 'npm run build',
  packageJsonPath: './package.json',
  
  // 缓存配置
  cache: {
    // 启用 ISR 缓存
    incrementalCache: {
      provider: 'cloudflare-kv',
      config: {
        keyPrefix: 'cache:',
        maxAge: 3600, // 1小时
      },
    },
  },
  
  // 图片优化（禁用，使用 Cloudflare Images）
  imageOptimization: {
    enabled: false,
  },
  
  // 环境变量
  env: {
    // 从 .env 文件加载
    loadEnvConfig: true,
  },
  
  // 调试选项
  debug: process.env.NODE_ENV === 'development',
}

export default config
