/**
 * 营销日历网站动画配置
 * 统一的动画参数，确保品牌一致性
 */

// 动画时长配置
export const DURATIONS = {
  fast: 200,      // 微交互 (hover, click feedback)
  normal: 400,    // 页面切换, 筛选
  slow: 600,      // 复杂过渡, 共享元素
  loading: 1500,  // 加载动画循环
} as const

// 缓动函数配置
export const EASINGS = {
  // Material Design 标准缓动
  standard: "cubic-bezier(0.4, 0.0, 0.2, 1)",
  decelerate: "cubic-bezier(0.0, 0.0, 0.2, 1)", 
  accelerate: "cubic-bezier(0.4, 0.0, 1, 1)",
  
  // 自定义缓动
  smooth: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
  bounce: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
  elastic: "cubic-bezier(0.175, 0.885, 0.32, 1.275)",
} as const

// 品牌动画变体
export const BRAND_VARIANTS = {
  // 淡入淡出
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: DURATIONS.normal / 1000, ease: EASINGS.standard }
  },
  
  // 从下方滑入
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: DURATIONS.normal / 1000, ease: EASINGS.decelerate }
  },
  
  // 缩放效果
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: DURATIONS.fast / 1000, ease: EASINGS.smooth }
  },
  
  // 错开动画
  stagger: {
    animate: {
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  }
} as const

// 月份切换动画配置
export const MONTH_TRANSITION = {
  duration: DURATIONS.normal / 1000,
  ease: EASINGS.standard,
  
  // 向前切换 (下个月)
  forward: {
    initial: { x: "100%", opacity: 0 },
    animate: { x: "0%", opacity: 1 },
    exit: { x: "-100%", opacity: 0 }
  },
  
  // 向后切换 (上个月)
  backward: {
    initial: { x: "-100%", opacity: 0 },
    animate: { x: "0%", opacity: 1 },
    exit: { x: "100%", opacity: 0 }
  }
} as const

// 事件筛选动画配置
export const FILTER_ANIMATION = {
  // 隐藏非匹配事件
  hideNonMatching: {
    scale: 0.8,
    opacity: 0,
    transition: {
      duration: DURATIONS.fast / 1000,
      ease: EASINGS.accelerate
    }
  },
  
  // 显示匹配事件
  showMatching: {
    scale: 1,
    opacity: 1,
    transition: {
      duration: DURATIONS.normal / 1000,
      ease: EASINGS.decelerate,
      delay: 0.1
    }
  },
  
  // 错开效果
  stagger: {
    transition: {
      staggerChildren: 0.02,
      delayChildren: 0.05
    }
  }
} as const

// 加载状态动画
export const LOADING_ANIMATIONS = {
  // 骨架屏闪烁
  skeleton: {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  },
  
  // 脉冲效果
  pulse: {
    animate: {
      scale: [1, 1.02, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  },
  
  // 进度条
  progressBar: {
    initial: { scaleX: 0 },
    animate: { scaleX: 1 },
    transition: {
      duration: DURATIONS.normal / 1000,
      ease: EASINGS.decelerate
    }
  }
} as const

// 响应式动画配置
export const RESPONSIVE_CONFIG = {
  // 移动端简化动画
  mobile: {
    reducedMotion: true,
    simplifyTransitions: true,
    fasterDurations: {
      fast: DURATIONS.fast * 0.7,
      normal: DURATIONS.normal * 0.7,
      slow: DURATIONS.slow * 0.7
    }
  },
  
  // 桌面端完整动画
  desktop: {
    fullAnimations: true,
    parallaxEffects: true,
    hoverStates: true
  }
} as const

// 可访问性配置
export const ACCESSIBILITY = {
  // 尊重用户的动画偏好
  respectReducedMotion: true,
  
  // 简化动画的回退方案
  reducedMotionFallback: {
    duration: 0.01, // 几乎瞬间完成
    ease: "linear"
  }
} as const
