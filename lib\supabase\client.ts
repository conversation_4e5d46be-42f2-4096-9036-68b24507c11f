import { createBrowserClient } from "@supabase/ssr"
import type { Database } from "@/types/supabase"
import { createMockClient } from "./mock-client"

// 创建一个单例客户端
let supabaseClient: any = null

// 创建浏览器端Supabase客户端
export const createClient = () => {
  if (supabaseClient) {
    return supabaseClient
  }

  try {
    // 使用服务器端API获取Supabase配置
    // 这样可以避免在客户端代码中直接引用环境变量
    const getSupabaseConfig = async () => {
      try {
        const response = await fetch("/api/supabase-config")
        if (!response.ok) {
          throw new Error("Failed to fetch Supabase configuration")
        }
        return await response.json()
      } catch (error) {
        console.error("Error fetching Supabase config:", error)
        return { url: "", key: "" }
      }
    }

    // 使用模拟客户端作为默认值
    supabaseClient = createMockClient()

    // 异步初始化真实客户端
    getSupabaseConfig().then(({ url, key }) => {
      if (url && key) {
        try {
          supabaseClient = createBrowserClient<Database>(url, key)
        } catch (error) {
          console.error("Error creating Supabase client:", error)
        }
      }
    })

    return supabaseClient
  } catch (error) {
    console.warn("创建Supabase客户端时出错:", error)
    return createMockClient()
  }
}
