#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 修复 Next.js 路由配置...');

// 检查 app 目录结构
function checkAppStructure() {
  const appDir = './app';
  console.log('\n📁 检查 app 目录结构:');
  
  function listDir(dir, level = 0) {
    const items = fs.readdirSync(dir);
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stats = fs.statSync(fullPath);
      const indent = '  '.repeat(level);
      
      if (stats.isDirectory()) {
        console.log(`${indent}📁 ${item}/`);
        if (level < 3) {
          listDir(fullPath, level + 1);
        }
      } else {
        console.log(`${indent}📄 ${item}`);
      }
    });
  }
  
  if (fs.existsSync(appDir)) {
    listDir(appDir);
  } else {
    console.log('❌ app 目录不存在');
  }
}

// 检查动态路由
function checkDynamicRoutes() {
  console.log('\n🔍 检查动态路由:');
  
  const routePatterns = [
    './app/calendar/[year]/[month]/page.tsx',
    './app/calendar/[year]/[month]/[day]/page.tsx',
    './app/events/[slug]/page.tsx',
    './app/events/[id]/page.tsx'
  ];
  
  routePatterns.forEach(pattern => {
    if (fs.existsSync(pattern)) {
      console.log(`✅ ${pattern} 存在`);
    } else {
      console.log(`❌ ${pattern} 不存在`);
    }
  });
}

// 检查 next.config.mjs
function checkNextConfig() {
  console.log('\n⚙️ 检查 Next.js 配置:');
  
  if (fs.existsSync('./next.config.mjs')) {
    const config = fs.readFileSync('./next.config.mjs', 'utf8');
    console.log('✅ next.config.mjs 存在');
    
    if (config.includes('trailingSlash')) {
      console.log('⚠️ 发现 trailingSlash 配置，可能影响路由');
    }
    
    if (config.includes('rewrites')) {
      console.log('✅ 发现 rewrites 配置');
    }
  } else {
    console.log('❌ next.config.mjs 不存在');
  }
}

// 主函数
function main() {
  console.log('🚀 开始路由诊断...\n');
  
  checkAppStructure();
  checkDynamicRoutes();
  checkNextConfig();
  
  console.log('\n✅ 路由诊断完成！');
  console.log('\n💡 建议:');
  console.log('1. 检查事件详情页路由是否正确');
  console.log('2. 确认动态路由文件存在');
  console.log('3. 检查 API 路由是否正常');
}

main();
