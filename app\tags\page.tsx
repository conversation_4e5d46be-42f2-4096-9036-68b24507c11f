import type { Metadata } from "next"
import Link from "next/link"
import { Tag } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { getAllTags } from "@/lib/api/events"

export const metadata: Metadata = {
  title: "事件标签 | 传统节日、公历节日、农历节日",
  description: "浏览所有事件标签，包括传统文化、农历节日、国际节日等。",
}

export default async function TagsPage() {
  const tags = await getAllTags()

  // 如果没有标签数据，显示一些示例标签
  const sampleTags =
    tags.length > 0
      ? tags
      : [
          { id: "1", name: "传统文化", slug: "traditional-culture" },
          { id: "2", name: "农历节日", slug: "lunar-festivals" },
          { id: "3", name: "国际节日", slug: "international-festivals" },
          { id: "4", name: "纪念活动", slug: "memorial-activities" },
          { id: "5", name: "二十四节气", slug: "solar-terms" },
          { id: "6", name: "法定假日", slug: "public-holidays" },
        ]

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">事件标签</h1>
      <p className="text-muted-foreground mb-8">浏览所有事件标签，点击任意标签查看相关事件。</p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sampleTags.map((tag) => (
          <Link key={tag.id} href={`/tag/${tag.slug}`}>
            <Card className="h-full hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Tag className="h-5 w-5" />
                  <h2 className="text-lg font-semibold">{tag.name}</h2>
                </div>
                <p className="text-sm text-muted-foreground">查看所有与{tag.name}相关的事件。</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}
