"use client"
import { MoreHorizontal } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { EVENT_TYPE_PRIORITY, type EventType } from "@/lib/types"
import { getCategoryBgColor, getCategoryColor, sortEventsByPriority } from "@/lib/utils"

interface DayEventsDemoProps {
  date: Date
}

export default function DayEventsDemo({ date }: DayEventsDemoProps) {
  // 生成10个随机事件，覆盖所有事件类型
  const events = generateRandomEvents(date, 10)

  // 按优先级排序事件
  const sortedEvents = sortEventsByPriority(events, EVENT_TYPE_PRIORITY)

  // 显示在单元格中的事件（最多5个）
  const displayEvents = sortedEvents.slice(0, 5)
  const hiddenEvents = sortedEvents.slice(5)
  const hasMoreEvents = hiddenEvents.length > 0

  return (
    <div className="border rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium">
          {date.toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "long",
            day: "numeric",
            weekday: "long",
          })}
        </h3>
        <div className="text-right p-1 font-medium text-sm bg-blue-500 text-white rounded-full w-7 h-7 flex items-center justify-center">
          {date.getDate()}
        </div>
      </div>

      {/* 事件列表 */}
      <div className="space-y-1 text-xs mb-4">
        {displayEvents.map((event) => (
          <div
            key={event.id}
            className="truncate rounded px-1 py-0.5"
            style={{ backgroundColor: getCategoryBgColor(event.type) }}
          >
            <span
              className="inline-block w-2 h-2 rounded-full mr-1"
              style={{ backgroundColor: getCategoryColor(event.type) }}
            ></span>
            {event.name}
          </div>
        ))}
      </div>

      {/* 更多事件指示器 */}
      {hasMoreEvents && (
        <div className="relative">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-5 w-5 p-0 absolute bottom-0 right-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-3 border-b">
                <h3 className="font-medium">
                  {date.toLocaleDateString("zh-CN", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                  })}
                </h3>
              </div>
              <div className="p-2 max-h-[300px] overflow-y-auto">
                {sortedEvents.map((event) => (
                  <div key={event.id} className="flex items-start gap-2 p-2 rounded hover:bg-muted">
                    <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white shrink-0">
                      {event.type}
                    </Badge>
                    <div>
                      <div className="font-medium">{event.name}</div>
                      {event.description && (
                        <div className="text-sm text-muted-foreground mt-1">{event.description}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      )}

      {/* 右侧显示完整事件列表 */}
      <div className="mt-8 border-t pt-4">
        <h4 className="font-medium mb-2">所有事件（按优先级排序）：</h4>
        <div className="space-y-2">
          {sortedEvents.map((event, index) => (
            <div key={event.id} className="flex items-center gap-2">
              <span className="text-xs font-medium">{index + 1}.</span>
              <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white">
                {event.type}
              </Badge>
              <span>{event.name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 生成随机事件
function generateRandomEvents(date: Date, count: number) {
  const eventTypes: EventType[] = ["公历", "农历", "重要事件", "国际节日", "节气", "纪念日", "会展", "品牌日", "娱乐"]

  const events = []

  // 确保每种类型至少有一个事件
  for (let i = 0; i < eventTypes.length && i < count; i++) {
    events.push({
      id: `demo-${i}`,
      name: `${eventTypes[i]}示例事件 ${i + 1}`,
      date: date.toISOString().split("T")[0],
      type: eventTypes[i],
      description: `这是一个${eventTypes[i]}类型的示例事件。`,
    })
  }

  // 如果需要更多事件，随机生成
  for (let i = eventTypes.length; i < count; i++) {
    const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    events.push({
      id: `demo-${i}`,
      name: `${randomType}示例事件 ${i + 1}`,
      date: date.toISOString().split("T")[0],
      type: randomType,
      description: `这是一个${randomType}类型的示例事件。`,
    })
  }

  return events
}
