import { format } from "date-fns"
import Link from "next/link"
import Image from "next/image"
import {
  Calendar,
  Clock,
  MapPin,
  Globe,
  User,
  Users,
  Tag,
  Repeat,
  AlertCircle,
  CheckCircle2,
  XCircle,
  PauseCircle,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { getCategoryColor } from "@/lib/utils"
import type { EventStatus } from "@/lib/types"

interface ExtendedEventDetailProps {
  event: any
  relatedEvents?: any[]
}

export default function ExtendedEventDetail({ event, relatedEvents = [] }: ExtendedEventDetailProps) {
  // 格式化日期
  const formatEventDate = () => {
    const startDate = new Date(event.date)

    if (event.endDate) {
      const endDate = new Date(event.endDate)
      return `${format(startDate, "yyyy年MM月dd日")} - ${format(endDate, "yyyy年MM月dd日")}`
    }

    return format(startDate, "yyyy年MM月dd日")
  }

  // 格式化时间
  const formatEventTime = () => {
    if (event.allDay) {
      return "全天"
    }

    return `${event.startTime || "00:00"} - ${event.endTime || "23:59"}`
  }

  // 获取状态图标和颜色
  const getStatusInfo = (status: EventStatus) => {
    switch (status) {
      case "active":
        return {
          icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
          label: "进行中",
          color: "bg-green-100 text-green-800",
        }
      case "cancelled":
        return { icon: <XCircle className="h-4 w-4 text-red-500" />, label: "已取消", color: "bg-red-100 text-red-800" }
      case "postponed":
        return {
          icon: <PauseCircle className="h-4 w-4 text-amber-500" />,
          label: "已推迟",
          color: "bg-amber-100 text-amber-800",
        }
      case "completed":
        return {
          icon: <CheckCircle2 className="h-4 w-4 text-blue-500" />,
          label: "已完成",
          color: "bg-blue-100 text-blue-800",
        }
      default:
        return {
          icon: <AlertCircle className="h-4 w-4 text-gray-500" />,
          label: "未知",
          color: "bg-gray-100 text-gray-800",
        }
    }
  }

  // 获取重复模式文本
  const getRecurrenceText = () => {
    if (!event.isRecurring) return null

    switch (event.recurrencePattern) {
      case "daily":
        return "每天"
      case "weekly":
        return "每周"
      case "monthly":
        return "每月"
      case "yearly":
        return "每年"
      case "custom":
        return `自定义 (${event.customRecurrenceRule || ""})`
      default:
        return "重复事件"
    }
  }

  const statusInfo = event.status ? getStatusInfo(event.status) : getStatusInfo("active")

  return (
    <div className="space-y-8">
      {/* 事件头部 */}
      <div className="space-y-4">
        <div className="flex flex-wrap items-center gap-2">
          <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white">
            {event.type}
          </Badge>

          {event.status && (
            <Badge className={statusInfo.color}>
              <span className="flex items-center gap-1">
                {statusInfo.icon}
                {statusInfo.label}
              </span>
            </Badge>
          )}

          {event.isRecurring && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Repeat className="h-3 w-3" />
              {getRecurrenceText()}
            </Badge>
          )}

          {event.priority && event.priority > 7 && <Badge variant="destructive">优先级: {event.priority}</Badge>}
        </div>

        <h1 className="text-3xl font-bold">{event.name}</h1>

        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {formatEventDate()}
          </div>

          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            {formatEventTime()}
          </div>

          {event.lunarDate && (
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              农历: {event.lunarDate}
            </div>
          )}

          {event.location && (
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              {event.location}
            </div>
          )}
        </div>
      </div>

      {/* 事件图片 */}
      {event.imageUrl && (
        <div className="rounded-lg overflow-hidden">
          <Image
            src={event.imageUrl || "/placeholder.svg"}
            alt={event.name}
            width={800}
            height={400}
            className="w-full h-auto object-cover"
          />
        </div>
      )}

      {/* 事件描述 */}
      {event.description && (
        <div className="prose max-w-none">
          <h2 className="text-xl font-semibold mb-2">事件描述</h2>
          <p>{event.description}</p>
        </div>
      )}

      <Separator />

      {/* 事件详情 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2 space-y-6">
          {/* 组织者和参与者 */}
          {(event.organizer || (event.attendees && event.attendees.length > 0)) && (
            <div>
              <h2 className="text-xl font-semibold mb-4">组织与参与</h2>

              {event.organizer && (
                <div className="flex items-start gap-2 mb-4">
                  <User className="h-5 w-5 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">组织者</div>
                    <div className="text-muted-foreground">{event.organizer}</div>
                  </div>
                </div>
              )}

              {event.attendees && event.attendees.length > 0 && (
                <div className="flex items-start gap-2">
                  <Users className="h-5 w-5 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">参与者</div>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {event.attendees.map((attendee: string, index: number) => (
                        <Badge key={index} variant="outline">
                          {attendee}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 重复信息 */}
          {event.isRecurring && (
            <div>
              <h2 className="text-xl font-semibold mb-4">重复设置</h2>
              <div className="flex items-start gap-2">
                <Repeat className="h-5 w-5 mt-0.5 text-muted-foreground" />
                <div>
                  <div className="font-medium">{getRecurrenceText()}</div>
                  {event.recurrenceEndDate && (
                    <div className="text-muted-foreground">
                      结束于: {format(new Date(event.recurrenceEndDate), "yyyy年MM月dd日")}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 标签 */}
          {event.tags && event.tags.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">标签</h2>
              <div className="flex flex-wrap gap-2">
                {event.tags.map((tag: string, index: number) => (
                  <Link key={index} href={`/tag/${encodeURIComponent(tag)}`}>
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {tag}
                    </Badge>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* 相关事件 */}
          {relatedEvents.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">相关事件</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {relatedEvents.map((relEvent) => (
                  <Link key={relEvent.id} href={`/event/${encodeURIComponent(relEvent.id)}`}>
                    <div className="border rounded-lg p-3 hover:bg-muted/50 transition-colors">
                      <div className="flex items-start gap-2">
                        <Badge
                          style={{ backgroundColor: getCategoryColor(relEvent.type) }}
                          className="text-white shrink-0 mt-0.5"
                        >
                          {relEvent.type}
                        </Badge>
                        <div>
                          <div className="font-medium">{relEvent.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(relEvent.date), "yyyy年MM月dd日")}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 侧边信息卡片 */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>事件信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {event.url && (
                <div className="flex items-start gap-2">
                  <Globe className="h-5 w-5 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">相关链接</div>
                    <a
                      href={event.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline break-all"
                    >
                      {event.url}
                    </a>
                  </div>
                </div>
              )}

              {event.source && (
                <div className="flex items-start gap-2">
                  <div className="font-medium">事件来源:</div>
                  <div className="text-muted-foreground">{event.source}</div>
                </div>
              )}

              {event.priority && (
                <div className="flex items-start gap-2">
                  <div className="font-medium">优先级:</div>
                  <div className="text-muted-foreground">{event.priority}/10</div>
                </div>
              )}

              <div className="pt-2">
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/calendar">
                    <Calendar className="mr-2 h-4 w-4" />
                    返回日历
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 如果有自定义颜色，显示颜色预览 */}
          {event.color && (
            <Card>
              <CardHeader>
                <CardTitle>自定义颜色</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="w-full h-10 rounded-md border" style={{ backgroundColor: event.color }}></div>
                <div className="text-center mt-2 font-mono text-sm">{event.color}</div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
