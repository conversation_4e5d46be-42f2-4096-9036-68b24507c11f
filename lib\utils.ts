import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import type { EventType } from "./types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getCategoryColor(category: EventType): string {
  switch (category) {
    case "娱乐":
      return "#ec4899" // pink-500
    case "公历":
      return "#0891b2" // cyan-600
    case "农历":
      return "#ca8a04" // yellow-600
    case "纪念日":
      return "#4f46e5" // indigo-600
    case "重要事件":
      return "#dc2626" // red-600
    case "国际节日":
      return "#7c3aed" // violet-600
    case "品牌日":
      return "#ea580c" // orange-600
    case "节气":
      return "#15803d" // green-700
    case "会展":
      return "#06b6d4" // cyan-500
    default:
      return "#6b7280" // gray-500 (fallback)
  }
}

// 获取分类的背景色（带透明度，适合作为背景）
export function getCategoryBgColor(category: EventType): string {
  switch (category) {
    case "娱乐":
      return "#fce7f3" // pink-100
    case "公历":
      return "#cffafe" // cyan-100
    case "农历":
      return "#fef9c3" // yellow-100
    case "纪念日":
      return "#e0e7ff" // indigo-100
    case "重要事件":
      return "#fee2e2" // red-100
    case "国际节日":
      return "#ede9fe" // violet-100
    case "品牌日":
      return "#ffedd5" // orange-100
    case "节气":
      return "#dcfce7" // green-100
    case "会展":
      return "#cffafe" // cyan-100
    default:
      return "#f3f4f6" // gray-100 (fallback)
  }
}

// 获取分类的文本颜色（适合在背景色上显示的文本）
export function getCategoryTextColor(category: EventType): string {
  switch (category) {
    case "娱乐":
      return "#be185d" // pink-800
    case "公历":
      return "#0e7490" // cyan-700
    case "农历":
      return "#854d0e" // yellow-800
    case "纪念日":
      return "#3730a3" // indigo-800
    case "重要事件":
      return "#991b1b" // red-800
    case "国际节日":
      return "#5b21b6" // violet-800
    case "品牌日":
      return "#9a3412" // orange-800
    case "节气":
      return "#166534" // green-800
    case "会展":
      return "#0e7490" // cyan-700
    default:
      return "#374151" // gray-700 (fallback)
  }
}

// 按优先级排序事件
export function sortEventsByPriority<T extends { type: string }>(
  events: T[],
  priorityMap: Record<string, number>,
): T[] {
  return [...events].sort((a, b) => {
    const priorityA = priorityMap[a.type] || 999
    const priorityB = priorityMap[b.type] || 999
    return priorityA - priorityB
  })
}
