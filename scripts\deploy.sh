#!/bin/bash

# 营销日历网站部署脚本
# 用于部署到 Cloudflare Workers

set -e

echo "🚀 开始部署营销日历网站到 Cloudflare Workers..."

# 检查必要的工具
check_dependencies() {
    echo "📋 检查依赖..."
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装"
        exit 1
    fi
    
    if ! command -v wrangler &> /dev/null; then
        echo "❌ wrangler 未安装，正在安装..."
        npm install -g wrangler
    fi
    
    echo "✅ 依赖检查完成"
}

# 构建应用
build_app() {
    echo "🔨 构建应用..."
    
    # 清理之前的构建
    rm -rf .next dist
    
    # 安装依赖
    npm ci
    
    # 类型检查
    npm run type-check
    
    # 构建 Next.js 应用
    npm run build:standalone
    
    echo "✅ 应用构建完成"
}

# 构建 Workers 适配器
build_workers_adapter() {
    echo "🔄 构建 Cloudflare Workers 适配器..."

    # 复制 Workers 文件
    cp workers/index.js dist/

    # 创建 manifest
    echo '{"version": "1.0.0", "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}' > dist/manifest.json

    echo "✅ Workers 适配器构建完成"
}

# 部署到 Cloudflare
deploy_to_cloudflare() {
    echo "☁️ 部署到 Cloudflare Workers..."
    
    # 检查环境
    if [ "$1" = "production" ]; then
        echo "🌟 部署到生产环境..."
        wrangler deploy --env production
    else
        echo "🧪 部署到开发环境..."
        wrangler deploy --env development
    fi
    
    echo "✅ 部署完成"
}

# 验证部署
verify_deployment() {
    echo "🔍 验证部署..."
    
    # 这里可以添加健康检查
    # curl -f https://your-domain.com/api/health
    
    echo "✅ 部署验证完成"
}

# 主函数
main() {
    local env=${1:-development}
    
    echo "🎯 部署环境: $env"
    
    check_dependencies
    build_app
    build_workers_adapter
    deploy_to_cloudflare $env
    verify_deployment
    
    echo "🎉 部署成功完成！"
    
    if [ "$env" = "production" ]; then
        echo "🌐 生产环境地址: https://your-domain.com"
    else
        echo "🧪 开发环境地址: https://marketing-calendar-dev.your-subdomain.workers.dev"
    fi
}

# 运行主函数
main $1
