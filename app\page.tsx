import { redirect } from "next/navigation"

export default function Home() {
  // Create a new Date object for the current time
  const now = new Date()

  // Get the year and month in Beijing time (Asia/Shanghai timezone)
  // 'en-US' locale with specific options provides reliable parts.
  // 'numeric' for year gives full year e.g., "2024"
  // '2-digit' for month gives zero-padded month e.g., "06"
  const yearInBeijing = now.toLocaleDateString("en-US", { timeZone: "Asia/Shanghai", year: "numeric" })
  const monthInBeijing = now.toLocaleDateString("en-US", { timeZone: "Asia/Shanghai", month: "2-digit" })

  // Construct the redirect path
  const redirectPath = `/calendar/${yearInBeijing}/${monthInBeijing}`

  redirect(redirectPath)
}
