/**
 * Web Vitals 监控
 * 监控核心 Web 指标以优化用户体验
 */

interface WebVitalMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
  navigationType?: string
}

class WebVitalsReporter {
  private metrics: WebVitalMetric[] = []
  private endpoint = '/api/analytics/web-vitals'
  private batchSize = 5
  private flushTimeout = 30000 // 30秒
  private timeoutId?: NodeJS.Timeout
  
  constructor() {
    this.initializeMetrics()
    this.setupFlushTimer()
  }
  
  private async initializeMetrics() {
    // 动态导入 web-vitals 以减少初始包大小
    try {
      const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals')
      
      getCLS(this.handleMetric.bind(this))
      getFID(this.handleMetric.bind(this))
      getFCP(this.handleMetric.bind(this))
      getLCP(this.handleMetric.bind(this))
      getTTFB(this.handleMetric.bind(this))
    } catch (error) {
      console.warn('Failed to load web-vitals:', error)
    }
  }
  
  private handleMetric(metric: WebVitalMetric) {
    this.metrics.push({
      ...metric,
      navigationType: this.getNavigationType()
    })
    
    // 立即发送关键指标
    if (['CLS', 'FID', 'LCP'].includes(metric.name)) {
      this.sendMetric(metric)
    }
    
    // 批量发送其他指标
    if (this.metrics.length >= this.batchSize) {
      this.sendBatch()
    }
  }
  
  private async sendMetric(metric: WebVitalMetric) {
    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metrics: [metric],
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          sessionId: this.getSessionId()
        }),
        keepalive: true
      })
    } catch (error) {
      console.error('Failed to send web vital metric:', error)
    }
  }
  
  private async sendBatch() {
    if (this.metrics.length === 0) return
    
    try {
      await fetch(this.endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metrics: this.metrics,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          sessionId: this.getSessionId()
        }),
        keepalive: true
      })
      
      this.metrics = []
    } catch (error) {
      console.error('Failed to send web vitals batch:', error)
    }
  }
  
  private setupFlushTimer() {
    this.timeoutId = setTimeout(() => {
      this.flush()
      this.setupFlushTimer() // 重新设置定时器
    }, this.flushTimeout)
  }
  
  private getNavigationType(): string {
    if (typeof window === 'undefined') return 'unknown'
    
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return navigation?.type || 'unknown'
  }
  
  private getSessionId(): string {
    // 简单的会话 ID 生成
    let sessionId = sessionStorage.getItem('webvitals-session-id')
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15)
      sessionStorage.setItem('webvitals-session-id', sessionId)
    }
    return sessionId
  }
  
  public flush() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
    }
    
    if (this.metrics.length > 0) {
      // 使用 sendBeacon 确保数据发送
      const data = JSON.stringify({
        metrics: this.metrics,
        url: window.location.href,
        timestamp: Date.now(),
        sessionId: this.getSessionId()
      })
      
      if (navigator.sendBeacon) {
        navigator.sendBeacon(this.endpoint, data)
      } else {
        // 降级到普通 fetch
        fetch(this.endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: data,
          keepalive: true
        }).catch(console.error)
      }
      
      this.metrics = []
    }
  }
}

let reporter: WebVitalsReporter | null = null

/**
 * 初始化 Web Vitals 监控
 */
export function initWebVitals() {
  if (typeof window === 'undefined') return
  if (reporter) return // 避免重复初始化
  
  reporter = new WebVitalsReporter()
  
  // 页面卸载时发送数据
  window.addEventListener('beforeunload', () => {
    reporter?.flush()
  })
  
  // 页面隐藏时发送数据
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      reporter?.flush()
    }
  })
  
  // 页面冻结时发送数据（移动端）
  window.addEventListener('freeze', () => {
    reporter?.flush()
  })
}

/**
 * 手动发送指标
 */
export function flushWebVitals() {
  reporter?.flush()
}
