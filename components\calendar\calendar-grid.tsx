"use client"
import { memo, useMemo } from "react"
import { MoreHorizontal } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { Event, EventType } from "@/lib/types"
import { getCategoryColor, getCategoryBgColor, sortEventsByPriority } from "@/lib/utils"
import { useMediaQuery } from "@/hooks/use-media-query"
import { EVENT_TYPE_PRIORITY } from "@/lib/types"
import Link from "next/link"
import { FilterableEvent, FilterableEventList } from "@/components/animations/filter-animation"
import { EventCardShared } from "@/components/animations/shared-element-transition"

// 在文件顶部导入农历工具函数
import { getSimpleLunarDate, getSpecialLunarEvents } from "@/lib/utils/lunar-calendar"

// 记忆化的日历单元格组件
const CalendarCell = memo(
  ({
    day,
    isCurrentMonth,
    events,
    today,
    isMobile,
  }: {
    day: Date
    isCurrentMonth: boolean
    events: Event[]
    today: Date
    isMobile: boolean
  }) => {
    const isToday = day.getTime() === today.getTime()
    const isPast = day < today

    // 按优先级排序事件
    const sortedEvents = useMemo(() => {
      return sortEventsByPriority(events, EVENT_TYPE_PRIORITY)
    }, [events])

    // 显示在单元格中的事件（最多5个）
    const displayEvents = sortedEvents.slice(0, 5)
    const hasMoreEvents = sortedEvents.length > 5

    // 获取农历日期
    const lunarDate = useMemo(() => {
      // 查找当天是否有带农历日期的事件
      const eventWithLunarDate = events.find((event) => event.lunarDate)
      if (eventWithLunarDate?.lunarDate) {
        return eventWithLunarDate.lunarDate
      }
      // 如果没有事件带农历日期，则使用计算的农历日期
      return getSpecialLunarEvents(day) || getSimpleLunarDate(day)
    }, [day, events])

    return (
      <div
        className={cn(
          "min-h-[100px] p-1 relative",
          !isCurrentMonth && "bg-muted/50 text-muted-foreground",
          isPast && isCurrentMonth && "bg-gray-50 text-gray-500", // 过去的日期加灰
          isToday && "bg-blue-50 ring-2 ring-blue-500 ring-inset", // 突出显示当天
        )}
      >
        {/* 日期行：农历日期（左）和公历日期（右）并排 */}
        <div className="flex justify-between items-center mb-1">
          {/* 农历日期（左对齐） */}
          <div
            className={cn(
              "text-xs text-gray-500",
              isPast && !isToday && "text-gray-400",
              isToday && "text-blue-600 font-medium",
            )}
          >
            {lunarDate}
          </div>

          {/* 公历日期（右对齐） */}
          <div
            className={cn(
              "font-medium text-sm",
              isToday && "bg-blue-500 text-white rounded-full w-7 h-7 flex items-center justify-center",
              isPast && !isToday && "text-gray-400", // 过去的日期文字颜色变淡
            )}
          >
            {day.getDate()}
          </div>
        </div>

        {/* 事件列表 */}
        <FilterableEventList className="mt-1 space-y-1 text-xs">
          {displayEvents.map((event, index) => (
            <FilterableEvent
              key={event.id}
              isVisible={true}
              delay={index * 0.02}
              layoutId={`event-${event.id}-${day.toISOString()}`}
            >
              <EventCardShared
                event={event}
                className={cn(
                  "block truncate rounded px-1 py-0.5 hover:brightness-95 transition-all",
                  isPast && !isToday && "opacity-60", // 过去的事件透明度降低
                )}
                style={{ backgroundColor: getCategoryBgColor(event.type) }}
              >
                <span
                  className="inline-block w-2 h-2 rounded-full mr-1"
                  style={{ backgroundColor: getCategoryColor(event.type) }}
                ></span>
                {event.name}
              </EventCardShared>
            </FilterableEvent>
          ))}
        </FilterableEventList>

        {/* 更多事件指示器 */}
        {hasMoreEvents &&
          (isMobile ? (
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute bottom-1 right-1 text-xs py-0.5 px-2 h-auto flex items-center gap-1 shadow-sm"
                >
                  <span>更多</span>
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom">
                <SheetHeader>
                  <SheetTitle>
                    {day.toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      weekday: "long",
                    })}
                  </SheetTitle>
                </SheetHeader>
                <div className="mt-4 space-y-2 max-h-[60vh] overflow-y-auto">
                  {sortedEvents.map((event) => (
                    <Link key={event.id} href={`/event/${encodeURIComponent(event.slug || event.id)}`} prefetch={false}>
                      <div className="flex items-start gap-2 p-2 rounded hover:bg-muted">
                        <Badge
                          style={{ backgroundColor: getCategoryColor(event.type) }}
                          className="text-white shrink-0"
                        >
                          {event.type}
                        </Badge>
                        <div>
                          <div className="font-medium">{event.name}</div>
                          {event.description ? (
                            <div className="text-sm text-muted-foreground mt-1">{event.description}</div>
                          ) : (
                            <div className="text-sm text-muted-foreground mt-1 italic">暂无描述</div>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute bottom-1 right-1 text-xs py-0.5 px-2 h-auto flex items-center gap-1 shadow-sm"
                >
                  <span>更多</span>
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <div className="p-3 border-b">
                  <h3 className="font-medium">
                    {day.toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      weekday: "long",
                    })}
                  </h3>
                </div>
                <div className="p-2 max-h-[300px] overflow-y-auto">
                  {sortedEvents.map((event) => (
                    <Link key={event.id} href={`/event/${encodeURIComponent(event.slug || event.id)}`} prefetch={false}>
                      <div className="flex items-start gap-2 p-2 rounded hover:bg-muted">
                        <Badge
                          style={{ backgroundColor: getCategoryColor(event.type) }}
                          className="text-white shrink-0"
                        >
                          {event.type}
                        </Badge>
                        <div>
                          <div className="font-medium">{event.name}</div>
                          {event.description ? (
                            <div className="text-sm text-muted-foreground mt-1">{event.description}</div>
                          ) : (
                            <div className="text-sm text-muted-foreground mt-1 italic">暂无描述</div>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          ))}
      </div>
    )
  },
)
CalendarCell.displayName = "CalendarCell"

// 记忆化的星期标题组件
const WeekdayHeader = memo(() => {
  // 星期几的标签
  const weekdays = ["日", "一", "二", "三", "四", "五", "六"]

  return (
    <div className="grid grid-cols-7 bg-muted">
      {weekdays.map((day, index) => (
        <div
          key={index}
          className={cn("py-2 text-center font-medium text-sm", (index === 0 || index === 6) && "text-red-500")}
        >
          {day}
        </div>
      ))}
    </div>
  )
})
WeekdayHeader.displayName = "WeekdayHeader"

interface CalendarGridProps {
  year: number
  month: number
  events: Event[]
  keyCategories: EventType[]
}

export default function CalendarGrid({ year, month, events, keyCategories }: CalendarGridProps) {
  const isMobile = useMediaQuery("(max-width: 768px)")

  // 使用useMemo计算日历数据，避免不必要的重新计算
  const calendarData = useMemo(() => {
    // 获取当月的第一天
    const firstDayOfMonth = new Date(year, month - 1, 1)

    // 获取当月的天数
    const daysInMonth = new Date(year, month, 0).getDate()

    // 获取当月第一天是星期几（0-6，0 表示星期日）
    const firstDayOfWeek = firstDayOfMonth.getDay()

    // 创建日历网格数据
    const calendarDays = []

    // 计算上个月的年份和月份
    let prevYear = year
    let prevMonth = month - 1
    if (prevMonth < 1) {
      prevMonth = 12
      prevYear--
    }

    // 计算下个月的年份和月份
    let nextYear = year
    let nextMonth = month + 1
    if (nextMonth > 12) {
      nextMonth = 1
      nextYear++
    }

    // 添加上个月的剩余天数
    const prevMonthDays = new Date(year, month - 1, 0).getDate()
    for (let i = 0; i < firstDayOfWeek; i++) {
      const day = prevMonthDays - firstDayOfWeek + i + 1
      const date = new Date(prevYear, prevMonth - 1, day)

      // 筛选上个月对应日期的事件
      const dayEvents = events.filter((event) => {
        const eventDate = new Date(event.date)
        return (
          eventDate.getDate() === day && eventDate.getMonth() === prevMonth - 1 && eventDate.getFullYear() === prevYear
        )
      })

      calendarDays.push({
        date,
        isCurrentMonth: false,
        events: dayEvents,
      })
    }

    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day)
      const dayEvents = events.filter((event) => {
        const eventDate = new Date(event.date)
        return eventDate.getDate() === day && eventDate.getMonth() === month - 1 && eventDate.getFullYear() === year
      })

      calendarDays.push({
        date,
        isCurrentMonth: true,
        events: dayEvents,
      })
    }

    // 添加下个月的开始天数，使日历网格填满 6 行
    const remainingDays = 42 - calendarDays.length
    for (let day = 1; day <= remainingDays; day++) {
      const date = new Date(nextYear, nextMonth - 1, day)

      // 筛选下个月对应日期的事件
      const dayEvents = events.filter((event) => {
        const eventDate = new Date(event.date)
        return (
          eventDate.getDate() === day && eventDate.getMonth() === nextMonth - 1 && eventDate.getFullYear() === nextYear
        )
      })

      calendarDays.push({
        date,
        isCurrentMonth: false,
        events: dayEvents,
      })
    }

    // 将日历天数分成 6 行
    const calendarRows = []
    for (let i = 0; i < calendarDays.length; i += 7) {
      calendarRows.push(calendarDays.slice(i, i + 7))
    }

    return calendarRows
  }, [year, month, events])

  // 获取今天的日期
  const today = useMemo(() => {
    const now = new Date()
    now.setHours(0, 0, 0, 0)
    return now
  }, [])

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* 星期标题行 */}
      <WeekdayHeader />

      {/* 日历网格 */}
      <div className="grid grid-cols-7 divide-x divide-y">
        {calendarData.map((row, rowIndex) =>
          row.map((day, colIndex) => (
            <CalendarCell
              key={`${rowIndex}-${colIndex}`}
              day={day.date}
              isCurrentMonth={day.isCurrentMonth}
              events={day.events}
              today={today}
              isMobile={isMobile}
            />
          )),
        )}
      </div>
    </div>
  )
}
