-- 数据库初始化脚本
-- 这个脚本会在 PostgreSQL 容器启动时自动执行

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建分类表
CREATE TABLE IF NOT EXISTS categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  color TEXT,
  priority INTEGER DEFAULT 999,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件表
CREATE TABLE IF NOT EXISTS events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  lunar_date TEXT,
  category_id UUID NOT NULL REFERENCES categories(id),
  image_url TEXT,
  image_alt_text TEXT,
  meta_title TEXT,
  meta_description TEXT,
  url_slug TEXT NOT NULL UNIQUE,
  h1_title TEXT,
  ai_summary TEXT,
  ai_faq JSONB,
  is_recurring BOOLEAN DEFAULT false,
  priority INTEGER DEFAULT 0,
  status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived')),
  all_day BOOLEAN DEFAULT TRUE,
  start_time TIME,
  end_time TIME,
  recurrence_pattern TEXT DEFAULT 'none' CHECK (recurrence_pattern IN ('none', 'daily', 'weekly', 'monthly', 'yearly', 'custom')),
  recurrence_end_date DATE,
  custom_recurrence_rule TEXT,
  color TEXT,
  url TEXT,
  organizer TEXT,
  attendees TEXT[],
  reminder INTEGER[],
  attachments JSONB,
  is_public BOOLEAN DEFAULT TRUE,
  source TEXT,
  related_events UUID[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建标签表
CREATE TABLE IF NOT EXISTS tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建事件标签关联表
CREATE TABLE IF NOT EXISTS event_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(event_id, tag_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_events_start_date ON events(start_date);
CREATE INDEX IF NOT EXISTS idx_events_end_date ON events(end_date);
CREATE INDEX IF NOT EXISTS idx_events_category_id ON events(category_id);
CREATE INDEX IF NOT EXISTS idx_events_status ON events(status);
CREATE INDEX IF NOT EXISTS idx_events_url_slug ON events(url_slug);
CREATE INDEX IF NOT EXISTS idx_events_is_recurring ON events(is_recurring);
CREATE INDEX IF NOT EXISTS idx_events_priority ON events(priority);
CREATE INDEX IF NOT EXISTS idx_events_is_public ON events(is_public);
CREATE INDEX IF NOT EXISTS idx_events_name_trgm ON events USING GIN (name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_ai_summary_trgm ON events USING GIN (ai_summary gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_categories_priority ON categories(priority);
CREATE INDEX IF NOT EXISTS idx_event_tags_event_id ON event_tags(event_id);
CREATE INDEX IF NOT EXISTS idx_event_tags_tag_id ON event_tags(tag_id);

-- 插入默认分类数据
INSERT INTO categories (name, slug, color, priority) VALUES
  ('公历', 'gregorian', '#0891b2', 1),
  ('农历', 'lunar', '#ca8a04', 2),
  ('重要事件', 'important-events', '#dc2626', 3),
  ('国际节日', 'international', '#7c3aed', 4),
  ('节气', 'solar-terms', '#15803d', 5),
  ('纪念日', 'memorial-days', '#4f46e5', 6),
  ('会展', 'exhibitions', '#06b6d4', 7),
  ('品牌日', 'brand-days', '#ea580c', 8),
  ('娱乐', 'entertainment', '#ec4899', 9)
ON CONFLICT (name) DO NOTHING;

-- 插入一些示例事件数据
DO $$
DECLARE
  cat_gregorian UUID;
  cat_lunar UUID;
  cat_international UUID;
  cat_memorial UUID;
  cat_brand UUID;
BEGIN
  -- 获取分类ID
  SELECT id INTO cat_gregorian FROM categories WHERE slug = 'gregorian';
  SELECT id INTO cat_lunar FROM categories WHERE slug = 'lunar';
  SELECT id INTO cat_international FROM categories WHERE slug = 'international';
  SELECT id INTO cat_memorial FROM categories WHERE slug = 'memorial-days';
  SELECT id INTO cat_brand FROM categories WHERE slug = 'brand-days';

  -- 插入示例事件
  INSERT INTO events (name, start_date, category_id, url_slug, status, lunar_date) VALUES
    ('元旦', '2025-01-01', cat_gregorian, 'new-year-day', 'published', NULL),
    ('春节', '2025-01-29', cat_lunar, 'spring-festival', 'published', '正月初一'),
    ('元宵节', '2025-02-12', cat_lunar, 'lantern-festival', 'published', '正月十五'),
    ('国际妇女节', '2025-03-08', cat_international, 'womens-day', 'published', NULL),
    ('植树节', '2025-03-12', cat_memorial, 'arbor-day', 'published', NULL),
    ('清明节', '2025-04-04', cat_lunar, 'qingming-festival', 'published', '三月初七'),
    ('劳动节', '2025-05-01', cat_gregorian, 'labor-day', 'published', NULL),
    ('母亲节', '2025-05-11', cat_international, 'mothers-day', 'published', NULL),
    ('端午节', '2025-05-31', cat_lunar, 'dragon-boat-festival', 'published', '五月初五'),
    ('父亲节', '2025-06-15', cat_international, 'fathers-day', 'published', NULL),
    ('中国共产党成立日', '2025-07-01', cat_memorial, 'cpc-founding-day', 'published', NULL),
    ('七夕节', '2025-08-29', cat_lunar, 'qixi-festival', 'published', '七月初七'),
    ('中秋节', '2025-10-06', cat_lunar, 'mid-autumn-festival', 'published', '八月十五'),
    ('国庆节', '2025-10-01', cat_gregorian, 'national-day', 'published', NULL),
    ('重阳节', '2025-10-29', cat_lunar, 'double-ninth-festival', 'published', '九月初九'),
    ('万圣节', '2025-10-31', cat_international, 'halloween', 'published', NULL),
    ('双十一购物节', '2025-11-11', cat_brand, 'singles-day', 'published', NULL),
    ('感恩节', '2025-11-27', cat_international, 'thanksgiving', 'published', NULL),
    ('圣诞节', '2025-12-25', cat_international, 'christmas', 'published', NULL)
  ON CONFLICT (url_slug) DO NOTHING;
END $$;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tags_updated_at BEFORE UPDATE ON tags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建一些有用的视图
CREATE OR REPLACE VIEW events_with_categories AS
SELECT 
  e.*,
  c.name as category_name,
  c.slug as category_slug,
  c.color as category_color,
  c.priority as category_priority
FROM events e
JOIN categories c ON e.category_id = c.id;

-- 创建统计视图
CREATE OR REPLACE VIEW event_statistics AS
SELECT 
  c.name as category_name,
  COUNT(e.id) as event_count,
  MIN(e.start_date) as earliest_event,
  MAX(e.start_date) as latest_event
FROM categories c
LEFT JOIN events e ON c.id = e.category_id AND e.status = 'published'
GROUP BY c.id, c.name, c.priority
ORDER BY c.priority;

-- 设置权限 (如果需要)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO calendar_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO calendar_user;

-- 完成初始化
SELECT 'Database initialization completed successfully!' as status;
