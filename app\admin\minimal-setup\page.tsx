"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react"

export default function MinimalSetupPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{
    success?: boolean
    message?: string
    error?: string
  } | null>(null)

  const runMigration = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch("/api/admin/minimal-migrate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || "最小数据迁移成功完成！",
        })
      } else {
        setResult({
          success: false,
          error: data.error || "数据迁移失败",
        })
      }
    } catch (error) {
      setResult({
        success: false,
        error: "请求失败，请检查网络连接",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>最小系统初始化</CardTitle>
          <CardDescription>运行最小数据迁移以初始化系统。这将创建必要的初始数据，包括分类和基本事件。</CardDescription>
        </CardHeader>
        <CardContent>
          {result && (
            <Alert className="mb-4" variant={result.success ? "default" : "destructive"}>
              {result.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
              <AlertTitle>{result.success ? "成功" : "错误"}</AlertTitle>
              <AlertDescription>{result.success ? result.message : result.error}</AlertDescription>
            </Alert>
          )}

          <p className="text-sm text-muted-foreground mb-4">
            点击下方按钮开始最小数据迁移。此操作只需执行一次，如果数据已存在，将不会重复创建。
          </p>
        </CardContent>
        <CardFooter>
          <Button onClick={runMigration} disabled={isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                正在迁移...
              </>
            ) : (
              "运行最小数据迁移"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
