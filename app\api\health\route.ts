import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    const startTime = Date.now()
    
    // 基本健康检查
    const health = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || "unknown",
      checks: {
        database: "unknown",
        memory: "healthy",
        disk: "healthy"
      }
    }

    // 内存使用检查
    const memUsage = process.memoryUsage()
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    }

    // 检查内存使用是否过高 (超过 1GB)
    if (memUsageMB.rss > 1024) {
      health.checks.memory = "warning"
    }

    // 数据库连接检查
    try {
      const supabase = await createClient()
      
      // 如果是 mock 客户端，跳过数据库检查
      if (supabase && !supabase.__isMockClient) {
        const { data, error } = await supabase
          .from("categories")
          .select("count(*)")
          .limit(1)
        
        if (error) {
          health.checks.database = "error"
          health.status = "degraded"
        } else {
          health.checks.database = "healthy"
        }
      } else {
        health.checks.database = "mock"
      }
    } catch (dbError) {
      console.error("Database health check failed:", dbError)
      health.checks.database = "error"
      health.status = "degraded"
    }

    // 响应时间检查
    const responseTime = Date.now() - startTime
    
    const response = {
      ...health,
      responseTime: `${responseTime}ms`,
      memory: memUsageMB,
      pid: process.pid,
      nodeVersion: process.version
    }

    // 根据健康状态返回适当的 HTTP 状态码
    const statusCode = health.status === "healthy" ? 200 : 
                      health.status === "degraded" ? 200 : 503

    return NextResponse.json(response, { status: statusCode })

  } catch (error) {
    console.error("Health check failed:", error)
    
    return NextResponse.json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : "Unknown error",
      checks: {
        database: "error",
        memory: "unknown",
        disk: "unknown"
      }
    }, { status: 503 })
  }
}
