#!/usr/bin/env node

/**
 * 部署测试脚本
 * 验证部署配置和基础功能
 */

const fs = require('fs')
const { execSync } = require('child_process')

console.log('🧪 部署配置测试\n')

// 测试项目
const tests = [
  {
    name: '检查环境配置文件',
    test: () => {
      const envExists = fs.existsSync('.env.local')
      if (envExists) {
        const envContent = fs.readFileSync('.env.local', 'utf8')
        const hasSupabase = envContent.includes('NEXT_PUBLIC_SUPABASE_URL')
        const hasCloudflare = envContent.includes('CLOUDFLARE_ACCOUNT_ID')
        console.log(`   .env.local: ✓`)
        console.log(`   Supabase 配置: ${hasSupabase ? '✓' : '✗'}`)
        console.log(`   Cloudflare 配置: ${hasCloudflare ? '✓' : '✗'}`)
        return envExists
      }
      return false
    }
  },
  {
    name: '检查 Wrangler 配置',
    test: () => {
      const wranglerExists = fs.existsSync('wrangler.toml')
      if (wranglerExists) {
        const wranglerContent = fs.readFileSync('wrangler.toml', 'utf8')
        const hasWorkerConfig = wranglerContent.includes('main = "workers/index.js"')
        const hasEnvConfig = wranglerContent.includes('[env.development]')
        console.log(`   wrangler.toml: ✓`)
        console.log(`   Worker 配置: ${hasWorkerConfig ? '✓' : '✗'}`)
        console.log(`   环境配置: ${hasEnvConfig ? '✓' : '✗'}`)
        return wranglerExists
      }
      return false
    }
  },
  {
    name: '检查 Workers 适配器',
    test: () => {
      const workerExists = fs.existsSync('workers/index.js')
      if (workerExists) {
        const workerContent = fs.readFileSync('workers/index.js', 'utf8')
        const hasExport = workerContent.includes('export default')
        const hasFetch = workerContent.includes('async fetch')
        console.log(`   workers/index.js: ✓`)
        console.log(`   导出配置: ${hasExport ? '✓' : '✗'}`)
        console.log(`   Fetch 处理: ${hasFetch ? '✓' : '✗'}`)
        return workerExists
      }
      return false
    }
  },
  {
    name: '检查部署脚本',
    test: () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      const scripts = packageJson.scripts || {}
      
      const hasDeployDev = 'deploy:dev' in scripts
      const hasDeployProd = 'deploy:prod' in scripts
      const hasSetupKV = 'setup:kv' in scripts
      
      console.log(`   deploy:dev: ${hasDeployDev ? '✓' : '✗'}`)
      console.log(`   deploy:prod: ${hasDeployProd ? '✓' : '✗'}`)
      console.log(`   setup:kv: ${hasSetupKV ? '✓' : '✗'}`)
      
      return hasDeployDev && hasDeployProd && hasSetupKV
    }
  },
  {
    name: '检查 Wrangler CLI',
    test: () => {
      try {
        // 尝试运行 wrangler whoami（不需要登录）
        const result = execSync('npx wrangler --version', { encoding: 'utf8', stdio: 'pipe' })
        console.log(`   Wrangler 版本: ${result.trim()}`)
        return true
      } catch (error) {
        console.log(`   Wrangler CLI: ✗ (${error.message})`)
        return false
      }
    }
  },
  {
    name: '检查构建配置',
    test: () => {
      const nextConfigExists = fs.existsSync('next.config.mjs')
      if (nextConfigExists) {
        const nextConfig = fs.readFileSync('next.config.mjs', 'utf8')
        const hasExport = nextConfig.includes("output: 'export'")
        const hasOptimizations = nextConfig.includes('optimizePackageImports')
        
        console.log(`   next.config.mjs: ✓`)
        console.log(`   导出配置: ${hasExport ? '✓' : '✗'}`)
        console.log(`   性能优化: ${hasOptimizations ? '✓' : '✗'}`)
        return nextConfigExists
      }
      return false
    }
  }
]

// 运行测试
let passed = 0
let failed = 0

for (const test of tests) {
  console.log(`🔍 ${test.name}`)
  
  try {
    const result = test.test()
    if (result) {
      console.log(`✅ 通过\n`)
      passed++
    } else {
      console.log(`❌ 失败\n`)
      failed++
    }
  } catch (error) {
    console.log(`❌ 错误: ${error.message}\n`)
    failed++
  }
}

// 输出结果
console.log('📊 测试结果:')
console.log(`✅ 通过: ${passed}`)
console.log(`❌ 失败: ${failed}`)
console.log(`📈 成功率: ${Math.round((passed / (passed + failed)) * 100)}%`)

// 提供建议
console.log('\n💡 下一步建议:')

if (failed === 0) {
  console.log('🎉 所有配置检查通过！')
  console.log('\n🚀 现在您可以：')
  console.log('1. 编辑 .env.local 文件，填入您的实际配置')
  console.log('2. 运行 "npx wrangler login" 登录 Cloudflare')
  console.log('3. 运行 "npm run setup:kv" 创建 KV 存储')
  console.log('4. 运行 "npm run deploy:dev" 部署到开发环境')
} else {
  console.log('⚠️ 部分配置需要完善，请检查上述失败项目')
  console.log('\n🔧 常见解决方案：')
  console.log('- 重新运行 "node scripts/setup-deployment.js"')
  console.log('- 检查文件权限和路径')
  console.log('- 确保 Node.js 和 npm 版本兼容')
}

process.exit(failed === 0 ? 0 : 1)
