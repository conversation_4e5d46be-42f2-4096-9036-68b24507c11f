import { getMockCategories, getMockEventsByMonth } from "../api/mock-data"

// 创建一个模拟的Supabase客户端，用于在环境变量不可用时提供数据
export function createMockClient() {
  return {
    from: (table: string) => ({
      select: (query?: string) => ({
        eq: (column: string, value: any) => ({
          single: () => {
            if (table === "categories") {
              const categories = getMockCategories()
              const category = categories.find((c) => c.slug === value || c.id === value)
              return Promise.resolve({ data: category, error: category ? null : { message: "Not found" } })
            }
            return Promise.resolve({ data: null, error: { message: "Not found" } })
          },
          order: (column: string, { ascending }: { ascending: boolean }) => {
            return Promise.resolve({ data: [], error: null })
          },
        }),
        order: (column: string, { ascending }: { ascending: boolean }) => {
          if (table === "categories") {
            const categories = getMockCategories()
            return Promise.resolve({ data: categories, error: null })
          }
          return Promise.resolve({ data: [], error: null })
        },
        limit: (limit: number) => {
          return Promise.resolve({ data: [], error: null })
        },
        gte: (column: string, value: any) => ({
          lte: (column: string, value: any) => ({
            order: (column: string, { ascending }: { ascending: boolean }) => {
              // 这里我们假设是在查询某个月的事件
              const match = value.match(/(\d{4})-(\d{2})-(\d{2})/)
              if (match) {
                const year = Number.parseInt(match[1])
                const month = Number.parseInt(match[2])
                const events = getMockEventsByMonth(year, month)

                // 转换为数据库格式
                const dbEvents = events.map((event) => ({
                  id: event.id,
                  name: event.name,
                  start_date: event.date,
                  lunar_date: event.lunarDate,
                  categories: {
                    id: "mock-id",
                    name: event.type,
                    slug: event.type.toLowerCase().replace(/\s+/g, "-"),
                    color: null,
                  },
                }))

                return Promise.resolve({ data: dbEvents, error: null })
              }
              return Promise.resolve({ data: [], error: null })
            },
          }),
        }),
        ilike: (column: string, value: any) => ({
          limit: (limit: number) => {
            return Promise.resolve({ data: [], error: null })
          },
        }),
      }),
      insert: (data: any) => {
        return Promise.resolve({ data: null, error: null })
      },
      update: (data: any) => ({
        eq: (column: string, value: any) => {
          return Promise.resolve({ data: null, error: null })
        },
      }),
    }),
    rpc: (func: string, params: any) => ({
      select: () => {
        return Promise.resolve({ data: [], error: null })
      },
    }),
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
    },
  } as any
}
