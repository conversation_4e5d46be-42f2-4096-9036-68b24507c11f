import type React from "react"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { AnimationProvider } from "@/components/animations/animation-provider"

import { PerformanceProvider } from "@/components/performance/performance-provider"
import dynamic from "next/dynamic"

// 动态导入 Footer 以减少初始包大小
const Footer = dynamic(() => import("@/components/layout/footer"), {
  ssr: true,
  loading: () => <div className="h-16 bg-gray-50" />
})

// 优化字体加载
const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  variable: '--font-inter',
  adjustFontFallback: true,
})



export const metadata: Metadata = {
  title: {
    default: "事件日历 | 传统节日、公历节日、农历节日",
    template: "%s | 事件日历",
  },
  description: "查看中国传统节日、公历节日、农历节日、国际节日等重要事件的日期、历史背景和相关信息。",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://yourdomain.com",
    siteName: "事件日历",
    images: [
      {
        url: "https://yourdomain.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "事件日历",
      },
    ],
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" className={inter.variable}>
      <head>
        {/* 预加载关键字体 */}
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        {/* DNS 预解析 */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* 性能优化 meta 标签 */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#ffffff" />
      </head>
      <body className={`${inter.className} font-smooth touch-manipulation`}>
        <PerformanceProvider>
          <AnimationProvider>
            <div className="flex flex-col min-h-screen">
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </AnimationProvider>

          {/* 性能监控 - 暂时禁用 */}
          {/* {process.env.NODE_ENV === 'development' && (
            <DynamicPerformanceMonitor />
          )} */}
        </PerformanceProvider>
      </body>
    </html>
  )
}
