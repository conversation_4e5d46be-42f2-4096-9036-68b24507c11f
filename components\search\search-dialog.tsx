"use client"

import type React from "react"
import { useState, useC<PERSON>back, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Search, Loader2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getCategoryColor } from "@/lib/utils"

interface SearchDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// 创建一个简单的内存缓存
const searchCache = new Map<string, { data: any[]; timestamp: number }>()
const CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟缓存过期

export default function SearchDialog({ open, onOpenChange }: SearchDialogProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [results, setResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 清理函数
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // 防抖搜索函数
  const debouncedSearch = useCallback((query: string) => {
    // 清除之前的超时
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    // 设置新的超时
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query)
    }, 300)
  }, [])

  // 执行搜索
  const performSearch = async (query: string) => {
    if (!query.trim() || query.trim().length < 2) {
      setResults([])
      return
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 检查缓存
    const cacheKey = query.trim().toLowerCase()
    const cachedResult = searchCache.get(cacheKey)
    if (cachedResult && Date.now() - cachedResult.timestamp < CACHE_EXPIRY) {
      setResults(cachedResult.data)
      return
    }

    setIsLoading(true)

    // 创建新的AbortController
    const controller = new AbortController()
    abortControllerRef.current = controller

    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`, {
        signal: controller.signal,
      })

      if (!response.ok) {
        throw new Error(`搜索失败: ${response.status}`)
      }

      const data = await response.json()
      const searchResults = data.results || []

      // 更新缓存
      searchCache.set(cacheKey, {
        data: searchResults,
        timestamp: Date.now(),
      })

      setResults(searchResults)
    } catch (error) {
      // 只有当不是因为取消请求而导致的错误才处理
      if (!(error instanceof DOMException && error.name === "AbortError")) {
        console.error("搜索错误:", error)
      }
    } finally {
      setIsLoading(false)
      abortControllerRef.current = null
    }
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchQuery(value)

    if (value.trim().length >= 2) {
      debouncedSearch(value)
    } else {
      setResults([])
    }
  }

  // 处理搜索表单提交
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim().length >= 2) {
      performSearch(searchQuery)
    }
  }

  // 处理结果点击
  const handleResultClick = (id: string) => {
    router.push(`/event/${encodeURIComponent(id)}`)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>搜索事件</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSearch} className="flex gap-2 mt-4">
          <Input
            placeholder="输入关键词搜索..."
            value={searchQuery}
            onChange={handleInputChange}
            className="flex-1"
            autoFocus
          />
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
          </Button>
        </form>

        <div className="mt-4 max-h-[300px] overflow-y-auto">
          {results.length > 0 ? (
            <div className="space-y-2">
              {results.map((result) => (
                <div
                  key={result.id}
                  className="p-3 border rounded-md hover:bg-muted cursor-pointer"
                  onClick={() => handleResultClick(result.id)}
                >
                  <div className="flex items-start gap-2">
                    <Badge
                      style={{ backgroundColor: getCategoryColor(result.type) }}
                      className="text-white shrink-0 mt-0.5"
                    >
                      {result.type}
                    </Badge>
                    <div>
                      <div className="font-medium">{result.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(result.date).toLocaleDateString("zh-CN")}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : searchQuery && !isLoading ? (
            <p className="text-center text-muted-foreground py-4">没有找到匹配的结果</p>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  )
}
