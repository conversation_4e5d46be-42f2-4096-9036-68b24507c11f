"use client"

import { useState, useEffect, use<PERSON>emo, use<PERSON><PERSON>back, memo } from "react"
import { useRouter } from "next/navigation"
import { ChevronLeft, ChevronRight, CalendarIcon, Search, Filter, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import type { EventType, Event } from "@/lib/types"
import { getCategoryColor } from "@/lib/utils"
import { useMediaQuery } from "@/hooks/use-media-query"
import dynamic from "next/dynamic"
import { DEFAULT_CATEGORIES } from "@/lib/types" // Ensure this is imported
import MonthTransition, { MonthNavButton, MonthTitle as AnimatedMonthTitle } from "@/components/animations/month-transition"
import { FilterButton, FilterResultCount, FilterIndicator } from "@/components/animations/filter-animation"

// 动态导入组件，减少初始加载包大小
const CalendarGrid = dynamic(() => import("./calendar-grid"), { ssr: true })
const DayView = dynamic(() => import("./day-view"), { ssr: true })
const CalendarInfoOverview = dynamic(() => import("./calendar-info-overview"), { ssr: true })
const SearchDialog = dynamic(() => import("@/components/search/search-dialog"), {
  ssr: false,
  loading: () => null,
})

interface CalendarViewProps {
  year: number
  month: number
  events: Event[]
  categories: any[]
}

// 创建一个记忆化的月份名称组件
const MonthTitle = memo(({ year, month }: { year: number; month: number }) => {
  const monthName = new Date(year, month - 1, 1).toLocaleString("zh-CN", { month: "long" })
  return (
    <h2 className="text-xl font-semibold">
      {year}年{monthName}
    </h2>
  )
})
MonthTitle.displayName = "MonthTitle"


FilterIndicator.displayName = "FilterIndicator"

export default function CalendarView({ year, month, events, categories = [] }: CalendarViewProps) {
  const router = useRouter()
  const [selectedCategories, setSelectedCategories] = useState<EventType[]>([])
  const [searchOpen, setSearchOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [viewMode, setViewMode] = useState<"month" | "day">("month")

  // 月份切换动画方向状态
  const [monthDirection, setMonthDirection] = useState<"forward" | "backward" | "none">("none")

  // 检测移动设备
  const isMobile = useMediaQuery("(max-width: 768px)")

  // 重置动画方向（在动画完成后）
  useEffect(() => {
    if (monthDirection !== "none") {
      const timer = setTimeout(() => {
        setMonthDirection("none")
      }, 500) // 稍微延迟以确保动画完成
      return () => clearTimeout(timer)
    }
  }, [monthDirection])

  // 根据设备设置视图模式
  useEffect(() => {
    if (isMobile) {
      setViewMode("day")
    } else {
      setViewMode("month")
    }
  }, [isMobile])

  // 初始化选中的分类 - 只在组件挂载和categories变化时执行
  useEffect(() => {
    if (categories.length > 0 && selectedCategories.length === 0) {
      // 只选择默认分类
      const defaultCats = categories
        .filter((c) => DEFAULT_CATEGORIES.includes(c.name as EventType))
        .map((c) => c.name as EventType)

      setSelectedCategories(defaultCats)
    }
  }, [categories]) // 移除selectedCategories.length依赖

  // 关键分类 - 记忆化计算
  const keyCategories = useMemo(() => {
    return categories.filter((c) => DEFAULT_CATEGORIES.includes(c.name as EventType)).map((c) => c.name as EventType)
  }, [categories])

  // 过滤事件 - 记忆化计算
  const filteredEvents = useMemo(() => {
    if (selectedCategories.length < categories.length && selectedCategories.length > 0) {
      return events.filter((event) => selectedCategories.includes(event.type))
    }
    return events
  }, [selectedCategories, events, categories.length])

  // 导航到指定月份 - 记忆化回调
  const navigateToMonth = useCallback(
    (newYear: number, newMonth: number) => {
      router.push(`/calendar/${newYear}/${newMonth.toString().padStart(2, "0")}`)
    },
    [router],
  )

  // 导航到上个月 - 记忆化回调
  const goToPreviousMonth = useCallback(() => {
    setMonthDirection("backward")
    let newYear = year
    let newMonth = month - 1

    if (newMonth < 1) {
      newMonth = 12
      newYear--
    }

    navigateToMonth(newYear, newMonth)
  }, [year, month, navigateToMonth])

  // 导航到下个月 - 记忆化回调
  const goToNextMonth = useCallback(() => {
    setMonthDirection("forward")
    let newYear = year
    let newMonth = month + 1

    if (newMonth > 12) {
      newMonth = 1
      newYear++
    }

    navigateToMonth(newYear, newMonth)
  }, [year, month, navigateToMonth])

  // 导航到今天 - 记忆化回调
  const goToToday = useCallback(() => {
    const today = new Date()
    navigateToMonth(today.getFullYear(), today.getMonth() + 1)
  }, [navigateToMonth])

  // 处理分类变更 - 记忆化回调
  const handleCategoryChange = useCallback((category: EventType, checked: boolean) => {
    if (checked) {
      setSelectedCategories((prev) => [...prev, category])
    } else {
      // 确保至少保留一个分类
      setSelectedCategories((prev) => (prev.length > 1 ? prev.filter((c) => c !== category) : prev))
    }
  }, [])

  // Renamed from clearFilters
  const restoreDefaultCategories = useCallback(() => {
    const defaultCats = categories
      .filter((c) => DEFAULT_CATEGORIES.includes(c.name as EventType))
      .map((c) => c.name as EventType)
    setSelectedCategories(defaultCats)
  }, [categories]) // Keep dependency on categories

  const selectAllCategories = useCallback(() => {
    const allCategoryNames = categories.map((c) => c.name as EventType)
    setSelectedCategories(allCategoryNames)
  }, [categories]) // Keep dependency on categories

  const isDefaultSelected = useMemo(() => {
    if (selectedCategories.length !== DEFAULT_CATEGORIES.length) return false
    // Sort copies to ensure order doesn't matter for comparison
    const sortedSelected = [...selectedCategories].sort()
    const sortedDefault = [...DEFAULT_CATEGORIES].sort()
    return sortedSelected.every((val, index) => val === sortedDefault[index])
  }, [selectedCategories])

  // 检查是否是当前月份 - 记忆化计算
  const isCurrentMonth = useMemo(() => {
    const today = new Date()
    return today.getFullYear() === year && today.getMonth() + 1 === month
  }, [year, month])

  // 切换到月视图 - 记忆化回调
  const switchToMonthView = useCallback(() => {
    setViewMode("month")
  }, [])

  return (
    <div className="space-y-6">
      {/* 信息概览 */}
      <CalendarInfoOverview year={year} month={month} events={events} keyCategories={keyCategories} />
      {/* 日历控制 */}
      <div className="flex flex-wrap justify-between items-center gap-4">
        <div className="flex items-center gap-2">
          <MonthNavButton
            onClick={goToPreviousMonth}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
            aria-label="上个月"
          >
            <ChevronLeft className="h-4 w-4" />
          </MonthNavButton>
          <MonthTitle year={year} month={month} />
          <MonthNavButton
            onClick={goToNextMonth}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10"
            aria-label="下个月"
          >
            <ChevronRight className="h-4 w-4" />
          </MonthNavButton>
          {!isCurrentMonth && (
            <Button variant="outline" size="sm" onClick={goToToday} className="ml-2">
              <CalendarIcon className="h-4 w-4 mr-2" />
              回到当月
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 视图切换 - 仅桌面 */}
          {!isMobile && (
            <Button
              variant={viewMode === "day" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode(viewMode === "month" ? "day" : "month")}
            >
              {viewMode === "month" ? "日视图" : "月视图"}
            </Button>
          )}

          {/* 搜索按钮 */}
          <Button variant="outline" size="icon" aria-label="搜索事件" onClick={() => setSearchOpen(true)}>
            <Search className="h-4 w-4" />
          </Button>

          {/* 搜索对话框 */}
          {searchOpen && <SearchDialog open={searchOpen} onOpenChange={setSearchOpen} />}

          {/* Filter Popover Trigger */}
          {categories.length > 0 && (
            <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <PopoverTrigger asChild>
                <FilterButton
                  isActive={selectedCategories.length < categories.length}
                  className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 w-10 ${
                    selectedCategories.length < categories.length
                      ? "bg-primary text-primary-foreground hover:bg-primary/90"
                      : "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                  }`}
                  aria-label="筛选事件"
                >
                  <Filter className="h-4 w-4" />
                </FilterButton>
              </PopoverTrigger>
              <PopoverContent className="w-80 max-h-[70vh] overflow-y-auto">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">按分类筛选</h3>
                    {!isDefaultSelected && (
                      <Button variant="ghost" size="sm" onClick={restoreDefaultCategories}>
                        恢复默认
                      </Button>
                    )}
                  </div>
                  {/* Checkbox mapping remains the same */}
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category.id}`}
                          checked={selectedCategories.includes(category.name)}
                          onCheckedChange={(checked) => handleCategoryChange(category.name, checked as boolean)}
                          disabled={selectedCategories.length === 1 && selectedCategories.includes(category.name)}
                        />
                        <label
                          htmlFor={`category-${category.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <span
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: category.color || getCategoryColor(category.name) }}
                          ></span>
                          {category.name}
                          {DEFAULT_CATEGORIES.includes(category.name) && (
                            <span className="ml-1 text-xs text-muted-foreground">(默认)</span>
                          )}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>
      {/* Filter Indicator */}
      <FilterIndicator
        categories={categories}
        selectedCategories={selectedCategories}
        onClearAll={selectAllCategories}
      />{" "}
      {/* 筛选状态指示器 */}
      <FilterIndicator
        isFiltering={selectedCategories.length < categories.length}
        activeFilters={selectedCategories || []}
        onClear={() => setSelectedCategories(categories.map(c => c.name))}
        className="mb-4"
      />

      {/* 筛选结果计数 */}
      {selectedCategories.length < categories.length && (
        <FilterResultCount
          count={filteredEvents.length}
          total={events.length}
          className="mb-4 text-sm text-muted-foreground text-center"
        />
      )}

      {/* 日历视图 */}
      {viewMode === "month" ? (
        <MonthTransition
          monthKey={`${year}-${month.toString().padStart(2, '0')}`}
          direction={monthDirection}
          className="w-full"
        >
          <CalendarGrid year={year} month={month} events={filteredEvents} keyCategories={keyCategories} />
        </MonthTransition>
      ) : (
        <DayView year={year} month={month} events={filteredEvents} onBackToMonthView={switchToMonthView} />
      )}
    </div>
  )
}
