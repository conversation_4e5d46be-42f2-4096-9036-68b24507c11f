/**
 * Bundle 分析工具
 * 用于分析和优化 JavaScript 包大小
 */

export class BundleAnalyzer {
  /**
   * 分析组件加载时间
   */
  static logComponentSize(componentName: string, startTime: number) {
    if (process.env.NODE_ENV !== 'development') return
    
    const endTime = performance.now()
    const loadTime = endTime - startTime
    
    if (loadTime > 100) {
      console.warn(`⚠️ Component ${componentName} took ${loadTime.toFixed(2)}ms to load`)
    } else {
      console.log(`✅ Component ${componentName} loaded in ${loadTime.toFixed(2)}ms`)
    }
  }
  
  /**
   * 监控动态导入性能
   */
  static async measureDynamicImport<T>(
    importFn: () => Promise<T>,
    componentName: string
  ): Promise<T> {
    const start = performance.now()
    
    try {
      const module = await importFn()
      this.logComponentSize(componentName, start)
      return module
    } catch (error) {
      console.error(`❌ Failed to load ${componentName}:`, error)
      throw error
    }
  }
  
  /**
   * 获取包大小信息
   */
  static getBundleInfo() {
    if (typeof window === 'undefined') return null
    
    return {
      // 获取性能导航信息
      navigation: performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming,
      // 获取资源加载信息
      resources: performance.getEntriesByType('resource') as PerformanceResourceTiming[],
      // 获取内存使用信息（如果支持）
      memory: (performance as any).memory || null
    }
  }
  
  /**
   * 分析关键资源
   */
  static analyzeCriticalResources() {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    
    const criticalResources = resources.filter(resource => {
      // 识别关键资源
      return resource.name.includes('_next/static/chunks/pages') ||
             resource.name.includes('_next/static/chunks/main') ||
             resource.name.includes('_next/static/css')
    })
    
    return criticalResources.map(resource => ({
      name: resource.name,
      size: resource.transferSize,
      loadTime: resource.responseEnd - resource.requestStart,
      type: this.getResourceType(resource.name)
    }))
  }
  
  private static getResourceType(url: string): string {
    if (url.includes('.js')) return 'javascript'
    if (url.includes('.css')) return 'stylesheet'
    if (url.includes('.woff') || url.includes('.ttf')) return 'font'
    if (url.includes('.png') || url.includes('.jpg') || url.includes('.webp')) return 'image'
    return 'other'
  }
}

/**
 * 性能监控装饰器
 */
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: any[]) => {
    const start = performance.now()
    const result = fn(...args)
    
    if (result instanceof Promise) {
      return result.finally(() => {
        BundleAnalyzer.logComponentSize(name, start)
      })
    } else {
      BundleAnalyzer.logComponentSize(name, start)
      return result
    }
  }) as T
}
