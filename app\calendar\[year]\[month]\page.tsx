import type { Metadata } from "next"
import { Suspense } from "react"
import CalendarView from "@/components/calendar/calendar-view"
import CalendarSkeleton from "@/components/calendar/calendar-skeleton"
import { createClient } from "@/lib/supabase/server"
import { getMockEventsByMonth, getMockCategories } from "@/lib/api/mock-data"
import PerformanceMonitor from "@/components/performance/performance-monitor"

// lib/supabase/mock-client.ts 最后会把标志挂到返回对象上
declare module "@/lib/supabase/mock-client" {
  interface MockClient {
    __isMockClient: true
  }
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ year: string; month: string }>
}): Promise<Metadata> {
  const { year: yearStr, month: monthStr } = await params
  const year = Number.parseInt(yearStr)
  const month = Number.parseInt(monthStr)

  const monthName = new Date(year, month - 1, 1).toLocaleString("zh-CN", { month: "long" })

  return {
    title: `${year}年${monthName}事件日历 | 节日与纪念日`,
    description: `查看${year}年${monthName}的所有重要事件、传统节日、公历节日、农历节日和国际节日。`,
  }
}

export default async function CalendarPage({
  params,
}: {
  params: Promise<{ year: string; month: string }>
}) {
  const { year: yearStr, month: monthStr } = await params
  const year = Number.parseInt(yearStr)
  const month = Number.parseInt(monthStr)

  return (
    <main className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 sr-only">事件日历</h1>

      <Suspense fallback={<CalendarSkeleton />}>
        <CalendarContent year={year} month={month} />
      </Suspense>

      {/* 添加性能监控组件 */}
      {process.env.NODE_ENV === "development" && <PerformanceMonitor />}
    </main>
  )
}

// 分离的异步组件，配合Suspense使用
async function CalendarContent({ year, month }: { year: number; month: number }) {
  // 获取日历数据
  const { events, categories } = await getCalendarData(year, month)

  return <CalendarView year={year} month={month} events={events} categories={categories} />
}

// 获取日历数据的函数
async function getCalendarData(year: number, month: number) {
  // 计算前后相邻月份（供 mock 数据使用）
  const prevDate = new Date(year, month - 2, 1) // 上月 1 号
  const nextDate = new Date(year, month, 1) // 下月 1 号

  // 如果无法连接 Supabase，直接走 mock 逻辑
  const fallback = () => {
    const events = [
      ...getMockEventsByMonth(prevDate.getFullYear(), prevDate.getMonth() + 1),
      ...getMockEventsByMonth(year, month),
      ...getMockEventsByMonth(nextDate.getFullYear(), nextDate.getMonth() + 1),
    ]
    const categories = getMockCategories()
    return { events, categories }
  }

  // ---------- EARLY EXIT ----------
  // If env-vars are missing we cannot query Supabase – use mock straight away
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return fallback()
  }

  let supabase: any
  try {
    supabase = await createClient()
  } catch (error) {
    console.error("Calendar: Failed to create real client", error)
    return fallback()
  }
  // 如果返回的是 mock-client，则直接使用后备数据
  if (!supabase || supabase.__isMockClient) {
    return fallback()
  }

  // 一个永远不会 throw 的安全查询包装
  const safeQuery = async <T,>(
    promise: Promise<{ data: T; error: any }>,
  ): Promise<{ data: T | null; error: any | null }> => {
    try {
      const { data, error } = await promise
      return { data, error }
    } catch (err) {
      return { data: null, error: err }
    }
  }

  try {
    // 查询日期范围
    const startDate = new Date(prevDate.getFullYear(), prevDate.getMonth(), 1).toISOString().split("T")[0]
    const endDate = new Date(nextDate.getFullYear(), nextDate.getMonth() + 1, 0).toISOString().split("T")[0]

    let eventsResult, categoriesResult
    try {
      ;[eventsResult, categoriesResult] = await Promise.all([
        safeQuery(
          supabase
            .from("events")
            .select(
              `
          id,
          name,
          start_date,
          lunar_date,
          categories:category_id(id, name, slug, color)
        `,
            )
            .gte("start_date", startDate)
            .lte("start_date", endDate)
            .order("start_date", { ascending: true }),
        ),
        safeQuery(
          supabase.from("categories").select("id, name, slug, color, priority").order("priority", { ascending: true }),
        ),
      ])
    } catch (e) {
      console.error("Supabase fetch threw, falling back to mock", e)
      return fallback()
    }

    // 如果任意一个请求出错，使用 mock 数据作为后备
    if (eventsResult.error || categoriesResult.error) {
      console.error("Supabase 请求失败，使用模拟数据：", eventsResult.error || categoriesResult.error)
      return fallback()
    }

    // —— 正常返回从 Supabase 获取的数据 ——
    const events =
      eventsResult.data?.map((e: any) => ({
        id: e.id,
        name: e.name,
        date: e.start_date,
        type: (e.categories?.name as any) || "未分类",
        lunarDate: e.lunar_date || undefined,
      })) ?? []

    const categories =
      categoriesResult.data?.map((c: any) => ({
        id: c.id,
        name: c.name,
        slug: c.slug,
        color: c.color || undefined,
        priority: c.priority || 999,
      })) ?? []

    return { events, categories }
  } catch (err) {
    // 最后的保障，任何异常均返回 mock 数据
    console.error("获取日历数据时发生未捕获错误，使用模拟数据：", err)
    return fallback()
  }
}
