'use client'

/**
 * 动态组件加载器
 * 提供性能优化的动态导入功能
 */

import dynamic from 'next/dynamic'
import { ComponentType, ReactNode, useState, useEffect, useRef } from 'react'
import { BundleAnalyzer } from '@/lib/performance/bundle-analyzer'

interface DynamicComponentOptions {
  loading?: () => ReactNode
  ssr?: boolean
  suspense?: boolean
}

/**
 * 创建性能优化的动态组件
 */
export function createDynamicComponent<T = {}>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  componentName: string,
  options: DynamicComponentOptions = {}
) {
  const {
    loading = () => <div className="animate-pulse bg-gray-200 h-20 rounded" />,
    ssr = true,
    suspense = false
  } = options
  
  return dynamic(
    () => BundleAnalyzer.measureDynamicImport(importFn, componentName),
    {
      loading,
      ssr,
      suspense
    }
  )
}

/**
 * 预定义的常用动态组件
 */

// 性能监控组件
export const DynamicPerformanceMonitor = createDynamicComponent(
  () => import('@/components/performance/performance-monitor'),
  'PerformanceMonitor',
  { ssr: false, loading: () => null }
)

/**
 * 条件动态加载组件
 */
export function ConditionalDynamicComponent<T = {}>({
  condition,
  component: Component,
  fallback = null,
  ...props
}: {
  condition: boolean
  component: ComponentType<T>
  fallback?: ReactNode
} & T) {
  if (!condition) {
    return <>{fallback}</>
  }
  
  return <Component {...(props as T)} />
}

/**
 * 延迟加载组件
 */
export function DelayedComponent({
  children,
  delay = 100,
  fallback = null
}: {
  children: ReactNode
  delay?: number
  fallback?: ReactNode
}) {
  const [shouldRender, setShouldRender] = useState(false)
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRender(true)
    }, delay)
    
    return () => clearTimeout(timer)
  }, [delay])
  
  return shouldRender ? <>{children}</> : <>{fallback}</>
}

/**
 * 可见性触发的动态加载
 */
export function VisibilityTriggeredComponent({
  children,
  threshold = 0.1,
  rootMargin = '50px',
  fallback = null
}: {
  children: ReactNode
  threshold?: number
  rootMargin?: string
  fallback?: ReactNode
}) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )
    
    if (ref.current) {
      observer.observe(ref.current)
    }
    
    return () => observer.disconnect()
  }, [threshold, rootMargin])
  
  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  )
}
