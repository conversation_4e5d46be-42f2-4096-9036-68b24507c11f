-- 扩展事件表，添加SEO相关字段
ALTER TABLE events 
  ADD COLUMN IF NOT EXISTS title TEXT,
  ADD COLUMN IF NOT EXISTS meta_description TEXT,
  ADD COLUMN IF NOT EXISTS faq JSONB;

-- 创建索引以提高搜索性能
CREATE INDEX IF NOT EXISTS idx_events_title_trgm ON events USING GIN (title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_events_meta_description_trgm ON events USING GIN (meta_description gin_trgm_ops);

-- 更新搜索函数以包含新字段
CREATE OR REPLACE FUNCTION search_events(query_text TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  title TEXT,
  start_date DATE,
  url_slug TEXT,
  category_id UUID,
  category_name TEXT,
  rank REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id,
    e.name,
    e.title,
    e.start_date,
    e.url_slug,
    e.category_id,
    c.name AS category_name,
    ts_rank_cd(
      setweight(to_tsvector('simple', e.name), 'A') ||
      setweight(to_tsvector('simple', COALESCE(e.title, '')), 'A') ||
      setweight(to_tsvector('simple', COALESCE(e.meta_description, '')), 'B') ||
      setweight(to_tsvector('simple', COALESCE(e.description, '')), 'B'),
      plainto_tsquery('simple', query_text)
    ) +
    similarity(e.name, query_text) * 2 +
    similarity(COALESCE(e.title, ''), query_text) * 2 +
    similarity(COALESCE(e.meta_description, ''), query_text) AS rank
  FROM events e
  JOIN categories c ON e.category_id = c.id
  WHERE 
    (
      e.name ILIKE '%' || query_text || '%' OR
      COALESCE(e.title, '') ILIKE '%' || query_text || '%' OR
      COALESCE(e.meta_description, '') ILIKE '%' || query_text || '%' OR
      COALESCE(e.description, '') ILIKE '%' || query_text || '%' OR
      EXISTS (
        SELECT 1 FROM event_tags et
        JOIN tags t ON et.tag_id = t.id
        WHERE et.event_id = e.id AND t.name ILIKE '%' || query_text || '%'
      )
    )
  ORDER BY rank DESC, e.priority DESC
  LIMIT 20;
END;
$$ LANGUAGE plpgsql;
