import type { Database } from "@/types/supabase"

// 定义前端使用的事件类型
export interface EventData {
  id: string
  name: string
  date: string
  type: string
  description?: string
  lunarDate?: string
  location?: string
  tags?: string[]
}

// 将Supabase事件数据转换为前端使用的格式
export function transformEventData(
  event: Database["public"]["Tables"]["events"]["Row"] & {
    categories: Database["public"]["Tables"]["categories"]["Row"]
    tags: Array<{ tags: Database["public"]["Tables"]["tags"]["Row"] }>
  },
): EventData {
  return {
    id: event.id,
    name: event.name,
    date: event.start_date,
    type: event.categories.name,
    description: event.ai_summary || undefined,
    lunarDate: event.lunar_date || undefined,
    location: undefined, // 可以从其他字段获取
    tags: event.tags.map((t) => t.tags.name),
  }
}

// 批量转换事件数据
export function transformEventsData(events: any[]): EventData[] {
  return events.map(transformEventData)
}
