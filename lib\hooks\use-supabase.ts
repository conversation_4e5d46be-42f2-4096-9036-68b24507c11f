"use client"

import { createClient } from "@/lib/supabase/client"
import { useEffect, useState } from "react"
import type { RealtimeChannel, User } from "@supabase/supabase-js"

// 创建一个自定义Hook来处理Supabase实时订阅
export function useSupabaseRealtime<T>(
  table: string,
  callback: (payload: { new: T; old: T }) => void,
  filter?: { field: string; value: string },
) {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)

  useEffect(() => {
    const supabase = createClient()

    // 创建实时订阅
    let subscription = supabase.channel(`${table}-changes`)

    // 添加过滤条件
    if (filter) {
      subscription = subscription.on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table,
          filter: `${filter.field}=eq.${filter.value}`,
        },
        (payload) => callback(payload as any),
      )
    } else {
      subscription = subscription.on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table,
        },
        (payload) => callback(payload as any),
      )
    }

    // 订阅并保存channel引用
    const channel = subscription.subscribe()
    setChannel(channel)

    // 清理函数
    return () => {
      supabase.removeChannel(channel)
    }
  }, [table, callback, filter])

  return channel
}

// 创建一个自定义Hook来处理Supabase认证
export function useSupabaseAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const supabase = createClient()

    // 获取当前会话
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user || null)
      setLoading(false)
    })

    // 监听认证状态变化
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return { user, loading }
}
