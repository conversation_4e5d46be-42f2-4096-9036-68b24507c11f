# 🎛️ 宝塔面板部署详细步骤

## 📋 **准备工作清单**

### ✅ **已完成**
- [x] Ubuntu 22.04 服务器
- [x] 宝塔面板已安装

### 📝 **需要准备**
- [ ] 域名（可选，可以先用 IP 访问）
- [ ] 服务器 IP 地址
- [ ] 宝塔面板登录信息

## 🚀 **详细部署步骤**

### **第一步：宝塔面板软件安装**

#### 1.1 登录宝塔面板
访问：`http://你的服务器IP:8888`

#### 1.2 安装必要软件
在 **软件商店** 中安装以下软件：

**必须安装**：
- ✅ **Nginx** 1.22+ 
- ✅ **PostgreSQL** 15.x （⚠️ 重要：选择 PostgreSQL，不要选择 MySQL）
- ✅ **Node.js 版本管理器** 
- ✅ **PM2 管理器** 4.x

**可选安装**：
- ✅ **Redis** 7.x （提升性能）
- ✅ **phpMyAdmin** （如果需要图形化数据库管理）

#### 1.3 配置 Node.js 环境
在 **终端** 中执行：
```bash
# 安装 Node.js 18
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
nvm alias default 18

# 安装 PM2
npm install -g pm2

# 验证安装
node --version
npm --version
pm2 --version
```

### **第二步：数据库配置**

#### 2.1 创建 PostgreSQL 数据库
在 **数据库** → **PostgreSQL** 中：
- 点击 **添加数据库**
- 数据库名：`calendar_db`
- 用户名：`calendar_user`
- 密码：`设置一个强密码并记住`

#### 2.2 配置数据库权限
在 **终端** 中执行：
```bash
# 切换到 postgres 用户
sudo -u postgres psql

# 给用户超级权限（用于创建扩展）
ALTER USER calendar_user WITH SUPERUSER;

# 退出
\q
```

### **第三步：项目文件上传**

#### 3.1 创建项目目录
在 **文件** 管理中：
- 进入 `/www/wwwroot/`
- 创建文件夹：`calendar`

#### 3.2 上传项目文件
将以下文件上传到 `/www/wwwroot/calendar/`：
- 所有项目源代码
- `package.json`
- `.env.bt-panel` 文件
- `ecosystem.config.json` 文件
- `bt-deploy.sh` 脚本

#### 3.3 设置文件权限
在 **终端** 中执行：
```bash
cd /www/wwwroot/calendar
chmod +x bt-deploy.sh
chown -R www:www /www/wwwroot/calendar
```

### **第四步：配置环境变量**

#### 4.1 创建环境配置
在 **文件** 管理中编辑 `/www/wwwroot/calendar/.env.local`：
```bash
# 复制模板文件
cp .env.bt-panel .env.local

# 编辑配置文件，修改以下关键配置：
# - POSTGRES_PASSWORD=你在第二步设置的数据库密码
# - NEXTAUTH_URL=http://你的域名或IP
# - NEXT_PUBLIC_BASE_URL=http://你的域名或IP
```

### **第五步：执行自动部署**

#### 5.1 运行部署脚本
在 **终端** 中执行：
```bash
cd /www/wwwroot/calendar
./bt-deploy.sh 你的域名或IP
```

#### 5.2 等待部署完成
脚本会自动执行：
- 安装项目依赖
- 构建项目
- 初始化数据库
- 启动应用

### **第六步：配置 Nginx 反向代理**

#### 6.1 创建网站
在 **网站** 中点击 **添加站点**：
- 域名：`你的域名` 或 `你的IP`
- 根目录：`/www/wwwroot/calendar/public`
- PHP版本：**纯静态**

#### 6.2 配置反向代理
在网站 **设置** → **反向代理**：
- 代理名称：`calendar-app`
- 目标URL：`http://127.0.0.1:3000`
- 发送域名：`$host`

#### 6.3 自定义 Nginx 配置
在网站 **设置** → **配置文件** 中，将 `bt-nginx-config.conf` 的内容复制进去，并替换域名。

### **第七步：验证部署**

#### 7.1 检查应用状态
在 **终端** 中执行：
```bash
# 检查 PM2 状态
pm2 status

# 检查应用日志
pm2 logs calendar-app

# 检查健康状态
curl http://localhost:3000/api/health
```

#### 7.2 访问测试
- 浏览器访问：`http://你的域名或IP`
- 健康检查：`http://你的域名或IP/health`

### **第八步：SSL 证书配置（可选）**

#### 8.1 申请 SSL 证书
在网站 **设置** → **SSL**：
- 选择 **Let's Encrypt**
- 输入邮箱地址
- 点击 **申请**

#### 8.2 强制 HTTPS
SSL 证书申请成功后：
- 开启 **强制HTTPS**
- 开启 **HSTS**

## 🔧 **管理和维护**

### **应用管理命令**
```bash
# 查看应用状态
pm2 status

# 重启应用
pm2 restart calendar-app

# 停止应用
pm2 stop calendar-app

# 查看实时日志
pm2 logs calendar-app --lines 100

# 监控应用
pm2 monit
```

### **数据库管理**
- 在宝塔面板 → **数据库** → **PostgreSQL** 中管理
- 或使用 pgAdmin 等工具连接

### **备份配置**
在 **计划任务** 中添加：

**数据库备份**（每天凌晨2点）：
```bash
pg_dump -h localhost -U calendar_user calendar_db > /www/backup/calendar_db_$(date +%Y%m%d).sql
```

**文件备份**（每周日凌晨3点）：
```bash
tar -czf /www/backup/calendar_files_$(date +%Y%m%d).tar.gz /www/wwwroot/calendar/uploads
```

## 🚨 **常见问题解决**

### **问题1：应用无法启动**
```bash
# 查看详细日志
pm2 logs calendar-app --lines 50

# 检查端口占用
netstat -tulpn | grep :3000

# 重新构建
cd /www/wwwroot/calendar
npm run build
pm2 restart calendar-app
```

### **问题2：数据库连接失败**
```bash
# 测试数据库连接
sudo -u postgres psql -d calendar_db -c "SELECT 1;"

# 检查数据库服务
systemctl status postgresql

# 重启数据库
systemctl restart postgresql
```

### **问题3：Nginx 配置错误**
- 在宝塔面板检查 Nginx 错误日志
- 测试配置：`nginx -t`
- 重载配置：`nginx -s reload`

### **问题4：权限问题**
```bash
# 修复文件权限
chown -R www:www /www/wwwroot/calendar
chmod -R 755 /www/wwwroot/calendar
```

## 📊 **性能监控**

### **系统监控**
在宝塔面板 → **监控** 中查看：
- CPU 使用率
- 内存使用率
- 磁盘使用率
- 网络流量

### **应用监控**
```bash
# PM2 监控
pm2 monit

# 查看进程
pm2 list

# 查看详细信息
pm2 show calendar-app
```

## 🎉 **部署完成**

部署成功后，您的日历应用将在以下地址运行：
- **主页**：`http://你的域名或IP`
- **健康检查**：`http://你的域名或IP/health`
- **管理后台**：`http://你的域名或IP/admin`

---

**如果遇到任何问题，请提供错误日志和具体现象，我将协助您解决！**
