"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { format } from "date-fns"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Clock, Plus, Trash2 } from "lucide-react"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

// 表单验证模式
const extendedEventFormSchema = z.object({
  name: z.string().min(2, { message: "事件名称至少需要2个字符" }),
  start_date: z.date({ required_error: "请选择开始日期" }),
  end_date: z.date().optional(),
  all_day: z.boolean().default(true),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  lunar_date: z.string().optional(),
  category_id: z.string().uuid({ message: "请选择有效的分类" }),
  url_slug: z
    .string()
    .min(3, { message: "URL标识至少需要3个字符" })
    .regex(/^[a-z0-9-]+$/, { message: "URL标识只能包含小写字母、数字和连字符" }),
  description: z.string().optional(),
  location: z.string().optional(),
  status: z
    .enum(["active", "cancelled", "postponed", "completed"] as const, {
      required_error: "请选择状态",
    })
    .default("active"),
  is_recurring: z.boolean().default(false),
  recurrence_pattern: z.enum(["none", "daily", "weekly", "monthly", "yearly", "custom"] as const).default("none"),
  recurrence_end_date: z.date().optional(),
  custom_recurrence_rule: z.string().optional(),
  color: z.string().optional(),
  priority: z.number().min(1).max(10).default(5),
  url: z.string().url({ message: "请输入有效的URL" }).optional().or(z.literal("")),
  organizer: z.string().optional(),
  attendees: z.array(z.string()).optional(),
  reminder: z.array(z.number()).optional(),
  is_public: z.boolean().default(true),
  source: z.string().optional(),
  image_url: z.string().optional(),
  related_events: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
})

type ExtendedEventFormValues = z.infer<typeof extendedEventFormSchema>

interface ExtendedEventFormProps {
  categories: any[]
  tags: any[]
  initialData?: any
}

export default function ExtendedEventForm({ categories, tags, initialData }: ExtendedEventFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedTab, setSelectedTab] = useState("basic")
  const [attendeeInput, setAttendeeInput] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>(initialData?.tags || [])

  // 默认值
  const defaultValues: Partial<ExtendedEventFormValues> = {
    name: initialData?.name || "",
    start_date: initialData?.start_date ? new Date(initialData.start_date) : new Date(),
    end_date: initialData?.end_date ? new Date(initialData.end_date) : undefined,
    all_day: initialData?.all_day !== undefined ? initialData.all_day : true,
    start_time: initialData?.start_time || "09:00",
    end_time: initialData?.end_time || "10:00",
    lunar_date: initialData?.lunar_date || "",
    category_id: initialData?.category_id || "",
    url_slug: initialData?.url_slug || "",
    description: initialData?.description || "",
    location: initialData?.location || "",
    status: initialData?.status || "active",
    is_recurring: initialData?.is_recurring || false,
    recurrence_pattern: initialData?.recurrence_pattern || "none",
    recurrence_end_date: initialData?.recurrence_end_date ? new Date(initialData.recurrence_end_date) : undefined,
    custom_recurrence_rule: initialData?.custom_recurrence_rule || "",
    color: initialData?.color || "",
    priority: initialData?.priority || 5,
    url: initialData?.url || "",
    organizer: initialData?.organizer || "",
    attendees: initialData?.attendees || [],
    reminder: initialData?.reminder || [],
    is_public: initialData?.is_public !== undefined ? initialData.is_public : true,
    source: initialData?.source || "",
    image_url: initialData?.image_url || "",
    related_events: initialData?.related_events || [],
    tags: initialData?.tags || [],
  }

  const form = useForm<ExtendedEventFormValues>({
    resolver: zodResolver(extendedEventFormSchema),
    defaultValues,
  })

  // 监听全天事件切换
  const watchAllDay = form.watch("all_day")
  const watchIsRecurring = form.watch("is_recurring")
  const watchRecurrencePattern = form.watch("recurrence_pattern")

  // 添加参与者
  const addAttendee = () => {
    if (attendeeInput.trim() && form.getValues("attendees")) {
      form.setValue("attendees", [...form.getValues("attendees")!, attendeeInput.trim()])
      setAttendeeInput("")
    } else if (attendeeInput.trim()) {
      form.setValue("attendees", [attendeeInput.trim()])
      setAttendeeInput("")
    }
  }

  // 移除参与者
  const removeAttendee = (index: number) => {
    const currentAttendees = form.getValues("attendees") || []
    form.setValue(
      "attendees",
      currentAttendees.filter((_, i) => i !== index),
    )
  }

  // 处理标签选择
  const handleTagToggle = (tagId: string) => {
    setSelectedTags((prev) => {
      if (prev.includes(tagId)) {
        return prev.filter((id) => id !== tagId)
      } else {
        return [...prev, tagId]
      }
    })
    form.setValue(
      "tags",
      selectedTags.includes(tagId) ? selectedTags.filter((id) => id !== tagId) : [...selectedTags, tagId],
    )
  }

  async function onSubmit(data: ExtendedEventFormValues) {
    setIsSubmitting(true)

    try {
      const supabase = createClient()

      // 准备数据
      const eventData = {
        name: data.name,
        start_date: format(data.start_date, "yyyy-MM-dd"),
        end_date: data.end_date ? format(data.end_date, "yyyy-MM-dd") : null,
        all_day: data.all_day,
        start_time: data.all_day ? null : data.start_time,
        end_time: data.all_day ? null : data.end_time,
        lunar_date: data.lunar_date || null,
        category_id: data.category_id,
        url_slug: data.url_slug,
        description: data.description || null,
        location: data.location || null,
        status: data.status,
        is_recurring: data.is_recurring,
        recurrence_pattern: data.is_recurring ? data.recurrence_pattern : "none",
        recurrence_end_date:
          data.recurrence_end_date && data.is_recurring ? format(data.recurrence_end_date, "yyyy-MM-dd") : null,
        custom_recurrence_rule:
          data.custom_recurrence_rule && data.recurrence_pattern === "custom" ? data.custom_recurrence_rule : null,
        color: data.color || null,
        priority: data.priority,
        url: data.url || null,
        organizer: data.organizer || null,
        attendees: data.attendees || null,
        reminder: data.reminder || null,
        is_public: data.is_public,
        source: data.source || null,
        image_url: data.image_url || null,
        related_events: data.related_events || null,
      }

      if (initialData?.id) {
        // 更新现有事件
        const { error } = await supabase.from("events").update(eventData).eq("id", initialData.id)

        if (error) throw error

        // 更新标签关联
        if (selectedTags.length > 0) {
          // 先删除现有关联
          await supabase.from("event_tags").delete().eq("event_id", initialData.id)

          // 添加新关联
          const tagLinks = selectedTags.map((tagId) => ({
            event_id: initialData.id,
            tag_id: tagId,
          }))

          const { error: tagError } = await supabase.from("event_tags").insert(tagLinks)
          if (tagError) throw tagError
        }

        toast({
          title: "事件已更新",
          description: "事件信息已成功更新。",
        })
      } else {
        // 创建新事件
        const { data: newEvent, error } = await supabase.from("events").insert(eventData).select("id").single()

        if (error) throw error

        // 添加标签关联
        if (selectedTags.length > 0 && newEvent) {
          const tagLinks = selectedTags.map((tagId) => ({
            event_id: newEvent.id,
            tag_id: tagId,
          }))

          const { error: tagError } = await supabase.from("event_tags").insert(tagLinks)
          if (tagError) throw tagError
        }

        toast({
          title: "事件已创建",
          description: "新事件已成功创建。",
        })

        // 重置表单
        form.reset(defaultValues)
        setSelectedTags([])
      }

      // 刷新页面
      router.refresh()
    } catch (error: any) {
      console.error("提交错误:", error)
      toast({
        title: "提交失败",
        description: error.message || "发生错误，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="details">详细信息</TabsTrigger>
            <TabsTrigger value="recurrence">重复设置</TabsTrigger>
            <TabsTrigger value="advanced">高级选项</TabsTrigger>
          </TabsList>

          {/* 基本信息 */}
          <TabsContent value="basic" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>事件名称 *</FormLabel>
                  <FormControl>
                    <Input placeholder="输入事件名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>开始日期 *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                          >
                            {field.value ? format(field.value, "yyyy-MM-dd") : <span>选择日期</span>}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>结束日期</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                          >
                            {field.value ? format(field.value, "yyyy-MM-dd") : <span>选择日期</span>}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          disabled={(date) => date < form.getValues("start_date")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>如果是单日事件，可以留空</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="all_day"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>全天事件</FormLabel>
                    <FormDescription>是否为全天事件</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            {!watchAllDay && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="start_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>开始时间</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <Input type="time" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>结束时间</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <Input type="time" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <FormField
              control={form.control}
              name="category_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分类 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lunar_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>农历日期</FormLabel>
                  <FormControl>
                    <Input placeholder="如：正月初一" {...field} />
                  </FormControl>
                  <FormDescription>如果是农历节日，请填写农历日期</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="url_slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL标识 *</FormLabel>
                  <FormControl>
                    <Input placeholder="如：spring-festival" {...field} />
                  </FormControl>
                  <FormDescription>用于生成事件页面的URL，只能包含小写字母、数字和连字符</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* 详细信息 */}
          <TabsContent value="details" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>事件描述</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入事件描述..." className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>地点</FormLabel>
                  <FormControl>
                    <Input placeholder="输入事件地点" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>状态</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="active">进行中</SelectItem>
                      <SelectItem value="cancelled">已取消</SelectItem>
                      <SelectItem value="postponed">已推迟</SelectItem>
                      <SelectItem value="completed">已完成</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="priority"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>优先级 (1-10)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={10}
                      {...field}
                      onChange={(e) => field.onChange(Number.parseInt(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>数字越大优先级越高，默认为5</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>相关链接</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>图片URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/image.jpg" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>标签</FormLabel>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                {tags.map((tag) => (
                  <div key={tag.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`tag-${tag.id}`}
                      checked={selectedTags.includes(tag.id)}
                      onCheckedChange={() => handleTagToggle(tag.id)}
                    />
                    <label
                      htmlFor={`tag-${tag.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {tag.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* 重复设置 */}
          <TabsContent value="recurrence" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="is_recurring"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>重复事件</FormLabel>
                    <FormDescription>是否为重复事件</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            {watchIsRecurring && (
              <>
                <FormField
                  control={form.control}
                  name="recurrence_pattern"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>重复模式</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择重复模式" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="daily">每天</SelectItem>
                          <SelectItem value="weekly">每周</SelectItem>
                          <SelectItem value="monthly">每月</SelectItem>
                          <SelectItem value="yearly">每年</SelectItem>
                          <SelectItem value="custom">自定义</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {watchRecurrencePattern === "custom" && (
                  <FormField
                    control={form.control}
                    name="custom_recurrence_rule"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>自定义重复规则</FormLabel>
                        <FormControl>
                          <Input placeholder="RRULE:FREQ=WEEKLY;INTERVAL=2;BYDAY=MO,WE,FR" {...field} />
                        </FormControl>
                        <FormDescription>使用iCalendar RRULE格式定义重复规则</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="recurrence_end_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>重复结束日期</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                            >
                              {field.value ? format(field.value, "yyyy-MM-dd") : <span>选择日期</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value || undefined}
                            onSelect={field.onChange}
                            disabled={(date) => date < form.getValues("start_date")}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>留空表示无限期重复</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}
          </TabsContent>

          {/* 高级选项 */}
          <TabsContent value="advanced" className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="organizer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>组织者</FormLabel>
                  <FormControl>
                    <Input placeholder="输入组织者名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>参与者</FormLabel>
              <div className="flex items-center space-x-2 mt-2">
                <Input
                  placeholder="添加参与者"
                  value={attendeeInput}
                  onChange={(e) => setAttendeeInput(e.target.value)}
                  className="flex-1"
                />
                <Button type="button" size="sm" onClick={addAttendee}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="mt-2 space-y-2">
                {form.getValues("attendees")?.map((attendee, index) => (
                  <div key={index} className="flex items-center justify-between bg-muted p-2 rounded-md">
                    <span className="text-sm">{attendee}</span>
                    <Button type="button" variant="ghost" size="sm" onClick={() => removeAttendee(index)}>
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>自定义颜色</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-2">
                      <Input type="color" {...field} className="w-12 h-10 p-1" />
                      <Input placeholder="#RRGGBB" value={field.value} onChange={field.onChange} className="flex-1" />
                    </div>
                  </FormControl>
                  <FormDescription>自定义事件颜色，留空则使用分类默认颜色</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_public"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>公开事件</FormLabel>
                    <FormDescription>是否公开显示此事件</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="source"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>事件来源</FormLabel>
                  <FormControl>
                    <Input placeholder="输入事件来源" {...field} />
                  </FormControl>
                  <FormDescription>事件的数据来源或引用</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "提交中..." : initialData ? "更新事件" : "创建事件"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
