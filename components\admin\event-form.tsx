"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { createClient } from "@/lib/supabase/client"
import { toast } from "@/components/ui/use-toast"

// 表单验证模式
const eventFormSchema = z.object({
  name: z.string().min(2, { message: "事件名称至少需要2个字符" }),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, { message: "日期格式应为YYYY-MM-DD" }),
  lunar_date: z.string().optional(),
  category_id: z.string().uuid({ message: "请选择有效的分类" }),
  url_slug: z
    .string()
    .min(3, { message: "URL标识至少需要3个字符" })
    .regex(/^[a-z0-9-]+$/, { message: "URL标识只能包含小写字母、数字和连字符" }),
  title: z.string().optional(),
  meta_description: z.string().optional(),
  description: z.string().optional(),
  location: z.string().optional(),
  tags: z.string().optional(),
  faq: z.string().optional(),
  status: z.enum(["draft", "published", "archived"], {
    required_error: "请选择状态",
  }),
})

type EventFormValues = z.infer<typeof eventFormSchema>

interface EventFormProps {
  categories: any[]
  initialData?: any
}

export default function EventForm({ categories, initialData }: EventFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")

  // 默认值
  const defaultValues: Partial<EventFormValues> = {
    name: initialData?.name || "",
    start_date: initialData?.start_date || new Date().toISOString().split("T")[0],
    lunar_date: initialData?.lunar_date || "",
    category_id: initialData?.category_id || "",
    url_slug: initialData?.url_slug || "",
    title: initialData?.title || "",
    meta_description: initialData?.meta_description || "",
    description: initialData?.description || "",
    location: initialData?.location || "",
    tags: initialData?.tags ? initialData.tags.join(", ") : "",
    faq: initialData?.faq ? JSON.stringify(initialData.faq, null, 2) : "",
    status: initialData?.status || "draft",
  }

  const form = useForm<EventFormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues,
  })

  async function onSubmit(data: EventFormValues) {
    setIsSubmitting(true)

    try {
      const supabase = createClient()

      // 处理标签
      const tags = data.tags
        ? data.tags
            .split(",")
            .map((tag) => tag.trim())
            .filter(Boolean)
        : null

      // 处理FAQ
      let faq = null
      if (data.faq) {
        try {
          faq = JSON.parse(data.faq)
        } catch (e) {
          toast({
            title: "FAQ格式错误",
            description: "请确保FAQ是有效的JSON格式",
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }
      }

      // 准备提交数据
      const eventData = {
        name: data.name,
        start_date: data.start_date,
        lunar_date: data.lunar_date || null,
        category_id: data.category_id,
        url_slug: data.url_slug,
        title: data.title || null,
        meta_description: data.meta_description || null,
        description: data.description || null,
        location: data.location || null,
        tags,
        faq,
        status: data.status,
      }

      if (initialData?.id) {
        // 更新现有事件
        const { error } = await supabase.from("events").update(eventData).eq("id", initialData.id)

        if (error) throw error

        toast({
          title: "事件已更新",
          description: "事件信息已成功更新。",
        })
      } else {
        // 创建新事件
        const { error } = await supabase.from("events").insert(eventData)

        if (error) throw error

        toast({
          title: "事件已创建",
          description: "新事件已成功创建。",
        })

        // 重置表单
        form.reset(defaultValues)
      }

      // 刷新页面
      router.refresh()
    } catch (error: any) {
      console.error("提交错误:", error)
      toast({
        title: "提交失败",
        description: error.message || "发生错误，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="seo">SEO信息</TabsTrigger>
            <TabsTrigger value="advanced">高级选项</TabsTrigger>
          </TabsList>

          {/* 基本信息 */}
          <TabsContent value="basic" className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>事件名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入事件名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>开始日期</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lunar_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>农历日期 (可选)</FormLabel>
                    <FormControl>
                      <Input placeholder="如：正月初一" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="category_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分类</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="url_slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL标识</FormLabel>
                  <FormControl>
                    <Input placeholder="如：spring-festival" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>事件描述</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入事件描述..." className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>地点</FormLabel>
                  <FormControl>
                    <Input placeholder="输入事件地点" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标签</FormLabel>
                  <FormControl>
                    <Input placeholder="标签1, 标签2, 标签3" {...field} />
                  </FormControl>
                  <FormDescription>多个标签请用逗号分隔</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* SEO信息 */}
          <TabsContent value="seo" className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO标题</FormLabel>
                  <FormControl>
                    <Input placeholder="输入SEO标题" {...field} />
                  </FormControl>
                  <FormDescription>如果留空，将使用事件名称作为标题</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="meta_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SEO描述</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入SEO描述" className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormDescription>如果留空，将使用事件描述作为SEO描述</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="faq"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>常见问题 (FAQ)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='[{"question":"问题1","answer":"回答1"},{"question":"问题2","answer":"回答2"}]'
                      className="min-h-[150px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>以JSON格式输入问题和答案</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* 高级选项 */}
          <TabsContent value="advanced" className="space-y-6">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>状态</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="draft">草稿</SelectItem>
                      <SelectItem value="published">已发布</SelectItem>
                      <SelectItem value="archived">已归档</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "提交中..." : initialData ? "更新事件" : "创建事件"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
