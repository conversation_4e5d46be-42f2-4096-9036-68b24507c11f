import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  // 创建响应对象
  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // 不创建Supabase客户端，只传递请求
  // 这将完全避免Supabase相关的错误

  return response
}

// 指定哪些路径需要应用中间件
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - 静态文件 (_next/static, _next/image, favicon.ico, 等)
     * - API 路由 (/api/*)
     */
    "/((?!_next/static|_next/image|favicon.ico|api/).+)",
  ],
}
