import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    console.log("Testing events query...")
    
    const supabase = await createClient()
    
    // 测试查询events表
    const { data: events, error: eventsError } = await supabase
      .from("events")
      .select(`
        id,
        name,
        start_date,
        lunar_date,
        categories:category_id(id, name, slug, color)
      `)
      .limit(10)
      .order("start_date", { ascending: true })
    
    if (eventsError) {
      console.error("Events query error:", eventsError)
      return NextResponse.json({
        success: false,
        error: "Events query failed",
        details: eventsError
      })
    }
    
    console.log("Events query successful, count:", events?.length)
    
    return NextResponse.json({
      success: true,
      message: "Events query successful",
      data: events,
      count: events?.length || 0
    })
    
  } catch (error) {
    console.error("Test events error:", error)
    return NextResponse.json({
      success: false,
      error: "Test events failed",
      details: error instanceof Error ? error.message : String(error)
    })
  }
}
