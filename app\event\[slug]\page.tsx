import type { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { Calendar, Clock, ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { getEventBySlug, getCategories } from "@/lib/api/events"
import { getCategoryColor } from "@/lib/utils"
import EventFAQ from "@/components/event/event-faq"
import EventStructuredData from "@/components/seo/event-structured-data"
import FAQStructuredData from "@/components/seo/faq-structured-data"

interface EventPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: EventPageProps): Promise<Metadata> {
  const { slug } = await params
  const event = await getEventBySlug(slug)
  const year = new Date(event.date).getFullYear()

  return {
    title: event.seoTitle || `${event.name} ${year} - ${event.type} | 事件日历`,
    description: event.metaDescription || `了解${event.name}的详细信息，包括日期、历史背景、习俗和活动。`,
    openGraph: {
      title: event.seoTitle || `${event.name} ${year}`,
      description: event.metaDescription || `了解${event.name}的详细信息，包括日期、历史背景、习俗和活动。`,
      type: "article",
      url: `https://yourdomain.com/event/${slug}`,
      images: [
        {
          url: `https://yourdomain.com/api/og?title=${encodeURIComponent(event.name)}`,
          width: 1200,
          height: 630,
          alt: event.name,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: event.seoTitle || `${event.name} ${year}`,
      description: event.metaDescription || `了解${event.name}的详细信息，包括日期、历史背景、习俗和活动。`,
      images: [`https://yourdomain.com/api/og?title=${encodeURIComponent(event.name)}`],
    },
  }
}

export default async function EventPage({ params }: EventPageProps) {
  const { slug } = await params
  const event = await getEventBySlug(slug)
  const categories = await getCategories()

  const eventCategory = categories.find((c) => c.name === event.type)
  const eventDate = new Date(event.date)
  const year = eventDate.getFullYear()
  const isEventPassed = eventDate < new Date()

  // 构建当前页面的完整URL
  const pageUrl = `https://yourdomain.com/event/${slug}`

  return (
    <>
      {/* 添加结构化数据 */}
      <EventStructuredData event={event} url={pageUrl} />
      {event.faq && <FAQStructuredData faq={event.faq} url={pageUrl} />}

      <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
        <div className="container mx-auto px-4 py-6">
          {/* 面包屑导航 */}
          <nav className="flex items-center text-sm mb-8" aria-label="面包屑导航">
            <Link href="/" className="text-muted-foreground hover:text-foreground transition-colors">
              首页
            </Link>
            <span className="mx-2 text-muted-foreground">/</span>
            <Link href="/calendar" className="text-muted-foreground hover:text-foreground transition-colors">
              事件日历
            </Link>
            <span className="mx-2 text-muted-foreground">/</span>
            <Link href={`/category/${eventCategory?.slug || ""}`} className="text-muted-foreground hover:text-foreground transition-colors">
              {event.type}
            </Link>
            <span className="mx-2 text-muted-foreground">/</span>
            <span className="text-foreground font-medium">{event.name}</span>
          </nav>

          {/* 1. 页面头部区域 (Hero Section) */}
          <section className="mb-12">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                {event.h1 || event.name} {year}
              </h1>

              {/* 事件标签和状态 */}
              <div className="flex flex-wrap justify-center gap-3 mb-6">
                <Badge
                  style={{ backgroundColor: getCategoryColor(event.type) }}
                  className="text-white px-4 py-2 text-sm font-medium"
                >
                  <Link href={`/category/${eventCategory?.slug || ""}`} className="hover:underline">
                    {event.type}
                  </Link>
                </Badge>

                {isEventPassed ? (
                  <Badge variant="outline" className="text-muted-foreground px-4 py-2">
                    已结束
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="px-4 py-2">
                    即将到来
                  </Badge>
                )}
              </div>

              {/* 核心日期信息 */}
              <div className="flex flex-col sm:flex-row justify-center items-center gap-4 text-lg">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <span className="font-medium">
                    {eventDate.toLocaleDateString("zh-CN", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      weekday: "long",
                    })}
                  </span>
                </div>

                {event.lunarDate && (
                  <>
                    <span className="hidden sm:block text-muted-foreground">•</span>
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-primary" />
                      <span className="text-muted-foreground">农历 {event.lunarDate}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </section>

          {/* 2. 核心信息栏 (Key Info Bar) */}
          <section className="mb-8">
            <div className="flex flex-wrap items-center justify-between gap-4 p-4 bg-muted/30 rounded-lg">
              <div className="flex flex-wrap items-center gap-6">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">分类:</span>
                  <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white">
                    {event.type}
                  </Badge>
                </div>
                {event.lunarDate && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">农历:</span>
                    <span className="font-medium">{event.lunarDate}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">状态:</span>
                  {isEventPassed ? (
                    <Badge variant="outline" className="text-muted-foreground">已结束</Badge>
                  ) : (
                    <Badge variant="secondary">即将到来</Badge>
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/calendar">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    返回日历
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/category/${eventCategory?.slug || ""}`}>
                    查看所有{event.type}
                  </Link>
                </Button>
              </div>
            </div>
          </section>

          {/* 3. 主要内容区域 (Main Content) - SEO优化结构 */}
          <section className="mb-12">
            <div className="grid lg:grid-cols-4 gap-8">
              <div className="lg:col-span-3">
                <div className="space-y-8">
                  {/* 事件介绍 */}
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold mb-6 flex items-center">
                        <div
                          className="w-1 h-6 rounded-full mr-3"
                          style={{ backgroundColor: getCategoryColor(event.type) }}
                        ></div>
                        {event.name}简介
                      </h2>

                      <div className="prose max-w-none prose-lg">
                        {event.content ? (
                          <div dangerouslySetInnerHTML={{ __html: event.content }} />
                        ) : (
                          <p className="text-muted-foreground leading-relaxed text-lg">
                            {event.metaDescription ||
                              `${event.name}是中国重要的${event.type}之一，每年${eventDate.toLocaleDateString("zh-CN", { month: "long", day: "numeric" })}${event.lunarDate ? `（农历${event.lunarDate}）` : ""}举行。作为传承千年的传统节日，${event.name}承载着深厚的文化内涵和历史意义，是中华民族文化传统的重要组成部分。`
                            }
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 历史背景 */}
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold mb-6">{event.name}的历史背景</h2>
                      <div className="prose max-w-none">
                        <p className="text-muted-foreground leading-relaxed">
                          {event.name}的历史可以追溯到古代，经过数千年的发展演变，形成了今天我们所熟知的节日形式。
                          这个节日不仅体现了古代人民的智慧，也反映了中华文化的深厚底蕴。
                        </p>
                        <p className="text-muted-foreground leading-relaxed mt-4">
                          在历史长河中，{event.name}见证了中华民族的兴衰变迁，承载着人们对美好生活的向往和追求。
                          每一次庆祝都是对传统文化的传承和发扬。
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 文化意义 */}
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold mb-6">{event.name}的文化意义</h2>
                      <div className="prose max-w-none">
                        <p className="text-muted-foreground leading-relaxed">
                          {event.name}不仅是一个时间节点，更是文化传承的重要载体。通过庆祝这个节日，
                          人们能够感受到传统文化的魅力，增强文化认同感和民族自豪感。
                        </p>
                        <p className="text-muted-foreground leading-relaxed mt-4">
                          在现代社会，{event.name}的文化价值愈发凸显，它连接着过去与现在，
                          为年轻一代了解和传承传统文化提供了重要途径。
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 庆祝方式 */}
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold mb-6">如何庆祝{event.name}</h2>
                      <div className="prose max-w-none">
                        <p className="text-muted-foreground leading-relaxed">
                          在{event.name}期间，人们通常会举行各种庆祝活动，包括传统仪式、文化表演、
                          家庭聚会等，这些活动不仅丰富了节日的内容，也加深了人们对传统文化的理解。
                        </p>
                        <div className="grid md:grid-cols-2 gap-4 mt-6">
                          <div className="bg-muted/30 rounded-lg p-4">
                            <h3 className="font-semibold mb-2">传统习俗</h3>
                            <p className="text-sm text-muted-foreground">
                              遵循古老的传统仪式和习俗，体验原汁原味的节日氛围。
                            </p>
                          </div>
                          <div className="bg-muted/30 rounded-lg p-4">
                            <h3 className="font-semibold mb-2">现代庆祝</h3>
                            <p className="text-sm text-muted-foreground">
                              结合现代元素，创新庆祝方式，让传统节日焕发新的活力。
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* 侧边栏 - 优化后 */}
              <div className="space-y-6">
                {/* 事件信息卡片 */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-4">事件信息</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-muted-foreground mb-1">事件类型</div>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: getCategoryColor(event.type) }}
                          ></div>
                          <span className="font-medium">{event.type}</span>
                        </div>
                      </div>

                      <div>
                        <div className="text-sm text-muted-foreground mb-1">公历日期</div>
                        <div className="font-medium">
                          {eventDate.toLocaleDateString("zh-CN", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                            weekday: "long"
                          })}
                        </div>
                      </div>

                      {event.lunarDate && (
                        <div>
                          <div className="text-sm text-muted-foreground mb-1">农历日期</div>
                          <div className="font-medium">农历 {event.lunarDate}</div>
                        </div>
                      )}

                      <div>
                        <div className="text-sm text-muted-foreground mb-1">距离天数</div>
                        <div className="font-medium">
                          {isEventPassed ? "已过去" : `还有 ${Math.ceil((eventDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} 天`}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 快速导航 */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-4">相关链接</h3>
                    <div className="space-y-3">
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link href="/calendar">
                          <Calendar className="h-4 w-4 mr-2" />
                          返回日历
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link href={`/category/${eventCategory?.slug || ""}`}>
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: getCategoryColor(event.type) }}
                          ></div>
                          更多{event.type}
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* SEO相关事件 */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-4">相关节日</h3>
                    <div className="space-y-3 text-sm">
                      <div className="text-muted-foreground">
                        探索更多与{event.name}相关的传统节日和重要纪念日，了解中华文化的丰富内涵。
                      </div>
                      <Button variant="outline" size="sm" className="w-full" asChild>
                        <Link href={`/category/${eventCategory?.slug || ""}`}>
                          查看所有{event.type}
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* 4. 互动区域 (Interactive Section) */}
          {event.faq && (
            <section className="mb-12">
              <Card>
                <CardContent className="p-8">
                  <h2 className="text-2xl font-bold mb-6 text-center">常见问题</h2>
                  <EventFAQ faq={event.faq} />
                </CardContent>
              </Card>
            </section>
          )}

          {/* 5. 页面底部 (Footer Actions) */}
          <section className="text-center">
            <div className="bg-muted/30 rounded-lg p-8">
              <h3 className="text-lg font-semibold mb-4">探索更多事件</h3>
              <p className="text-muted-foreground mb-6">
                发现更多有趣的传统节日和重要事件
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/calendar">
                    <Calendar className="h-4 w-4 mr-2" />
                    返回事件日历
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href={`/category/${eventCategory?.slug || ""}`}>
                    查看所有{event.type}
                  </Link>
                </Button>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  )
}
