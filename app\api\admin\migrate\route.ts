import { NextResponse } from "next/server"
import { runMigrations } from "@/lib/db/migrations"
import { createClient } from "@/lib/supabase/server"

// 此API路由用于手动触发数据迁移
export async function POST(request: Request) {
  try {
    // 验证管理员权限
    const supabase = createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // 检查用户角色
    const { data: userRole } = await supabase.from("profiles").select("role").eq("id", session.user.id).single()

    if (!userRole || userRole.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // 运行迁移
    const result = await runMigrations()

    if (result.success) {
      return NextResponse.json({ message: "Migration completed successfully" })
    } else {
      return NextResponse.json({ error: "Migration failed", details: result.error }, { status: 500 })
    }
  } catch (error) {
    console.error("Migration error:", error)
    return NextResponse.json({ error: "Migration failed" }, { status: 500 })
  }
}
