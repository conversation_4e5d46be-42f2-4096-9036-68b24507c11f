import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { getCategoryColor } from "@/lib/utils"
import type { Event } from "@/lib/types"

interface EventCardProps {
  event: Event
}

export default function EventCard({ event }: EventCardProps) {
  return (
    <Link href={`/event/${event.slug}`}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white">
                {event.type}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {new Date(event.date).toLocaleDateString("zh-CN", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
            </div>
            <h3 className="text-lg font-semibold mt-1">{event.name}</h3>
            {event.metaDescription && (
              <p className="text-sm text-muted-foreground line-clamp-2">{event.metaDescription}</p>
            )}
            {event.lunarDate && <div className="text-xs text-muted-foreground mt-1">农历: {event.lunarDate}</div>}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
