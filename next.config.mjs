/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用实验性特性
  experimental: {
    // React 19 并发特性
    reactCompiler: false, // 暂时关闭，等待稳定
    // 优化包导入
    optimizePackageImports: ['framer-motion', 'lucide-react', '@radix-ui/react-icons'],
    // 启用 Turbopack (开发环境)
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // 启用 PPR (Partial Prerendering)
    ppr: false, // 暂时关闭，等待稳定
  },

  // 编译优化
  compiler: {
    // 生产环境移除 console.log
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },

  // 图片优化 (为 Cloudflare 配置)
  images: {
    unoptimized: true, // Cloudflare Workers 不支持 next/image 优化
    formats: ['image/webp', 'image/avif'],
  },

  // 输出配置 - 为 Cloudflare Workers 优化
  output: 'export',
  trailingSlash: true,
  distDir: 'dist',

  // 性能优化
  poweredByHeader: false,
  compress: true,

  // 开发环境配置
  eslint: {
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // 安全和性能头部
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ]
  },

  // 重定向配置
  async redirects() {
    return [
      // 可以在这里添加重定向规则
    ]
  },

  // 重写配置
  async rewrites() {
    return [
      // 可以在这里添加重写规则
    ]
  },
}

export default nextConfig
