#!/usr/bin/env node

/**
 * 升级测试脚本
 * 验证 Next.js 15 升级后的功能
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 开始测试 Next.js 15 升级...\n')

// 测试配置
const tests = [
  {
    name: '检查 Next.js 版本',
    test: () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      const nextVersion = packageJson.dependencies.next
      console.log(`   Next.js 版本: ${nextVersion}`)
      return nextVersion.includes('15.')
    }
  },
  {
    name: '检查 React 版本',
    test: () => {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      const reactVersion = packageJson.dependencies.react
      console.log(`   React 版本: ${reactVersion}`)
      return reactVersion.includes('19') || reactVersion.includes('^19')
    }
  },
  {
    name: '检查 TypeScript 配置',
    test: () => {
      const tsConfigExists = fs.existsSync('tsconfig.json')
      if (tsConfigExists) {
        const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'))
        console.log(`   TypeScript 配置: ✓`)
        return true
      }
      return false
    }
  },
  {
    name: '检查 Next.js 配置',
    test: () => {
      const configExists = fs.existsSync('next.config.mjs')
      if (configExists) {
        const config = fs.readFileSync('next.config.mjs', 'utf8')
        const hasOptimizations = config.includes('optimizePackageImports')
        console.log(`   Next.js 配置优化: ${hasOptimizations ? '✓' : '✗'}`)
        return configExists
      }
      return false
    }
  },
  {
    name: '检查 Tailwind 配置',
    test: () => {
      const configExists = fs.existsSync('tailwind.config.ts')
      if (configExists) {
        const config = fs.readFileSync('tailwind.config.ts', 'utf8')
        const hasPerformanceOptimizations = config.includes('hoverOnlyWhenSupported')
        console.log(`   Tailwind 性能优化: ${hasPerformanceOptimizations ? '✓' : '✗'}`)
        return configExists
      }
      return false
    }
  },
  {
    name: '检查性能监控文件',
    test: () => {
      const files = [
        'lib/performance/web-vitals.ts',
        'lib/performance/bundle-analyzer.ts',
        'components/performance/performance-provider.tsx'
      ]
      
      const allExist = files.every(file => {
        const exists = fs.existsSync(file)
        console.log(`   ${file}: ${exists ? '✓' : '✗'}`)
        return exists
      })
      
      return allExist
    }
  },
  {
    name: '检查部署配置',
    test: () => {
      const files = [
        'wrangler.toml',
        'opennext.config.ts',
        'scripts/deploy.sh'
      ]
      
      const allExist = files.every(file => {
        const exists = fs.existsSync(file)
        console.log(`   ${file}: ${exists ? '✓' : '✗'}`)
        return exists
      })
      
      return allExist
    }
  },
  {
    name: '检查 API 路由',
    test: () => {
      const apiRoute = 'app/api/analytics/web-vitals/route.ts'
      const exists = fs.existsSync(apiRoute)
      console.log(`   Web Vitals API: ${exists ? '✓' : '✗'}`)
      return exists
    }
  },
  {
    name: '类型检查',
    test: () => {
      try {
        console.log('   运行 TypeScript 类型检查...')
        execSync('npx tsc --noEmit', { stdio: 'pipe' })
        console.log('   类型检查: ✓')
        return true
      } catch (error) {
        console.log('   类型检查: ✗')
        console.log('   错误:', error.stdout?.toString() || error.message)
        return false
      }
    }
  }
]

// 运行测试
let passed = 0
let failed = 0

for (const test of tests) {
  console.log(`🔍 ${test.name}`)
  
  try {
    const result = test.test()
    if (result) {
      console.log(`✅ 通过\n`)
      passed++
    } else {
      console.log(`❌ 失败\n`)
      failed++
    }
  } catch (error) {
    console.log(`❌ 错误: ${error.message}\n`)
    failed++
  }
}

// 输出结果
console.log('📊 测试结果:')
console.log(`✅ 通过: ${passed}`)
console.log(`❌ 失败: ${failed}`)
console.log(`📈 成功率: ${Math.round((passed / (passed + failed)) * 100)}%`)

if (failed === 0) {
  console.log('\n🎉 所有测试通过！Next.js 15 升级成功！')
  process.exit(0)
} else {
  console.log('\n⚠️ 部分测试失败，请检查上述错误')
  process.exit(1)
}
