import type { Metadata } from "next"
import Link from "next/link"
import { ArrowLeft, TagIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import EventCard from "@/components/event/event-card"

interface TagPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const tag = decodeURIComponent(params.slug.replace(/-/g, " "))

  return {
    title: `${tag}相关事件 | 事件日历`,
    description: `浏览所有与${tag}相关的事件，包括日期、详情和相关信息。`,
  }
}

export default async function TagPage({ params }: TagPageProps) {
  const tagSlug = params.slug
  const tagName = decodeURIComponent(tagSlug.replace(/-/g, " "))

  // 这里应该从API获取标签相关的事件
  // 由于我们还没有实现标签系统，这里使用示例数据
  const events = [
    {
      id: "1",
      name: "春节",
      date: "2024-02-10",
      type: "农历" as const,
      lunarDate: "正月初一",
      slug: "spring-festival",
      metaDescription: "春节是中国最重要的传统节日，农历正月初一，象征着新的一年的开始。",
    },
    {
      id: "2",
      name: "元宵节",
      date: "2024-02-24",
      type: "农历" as const,
      lunarDate: "正月十五",
      slug: "lantern-festival",
      metaDescription: "元宵节是中国传统节日，农历正月十五，是春节之后的第一个重要节日。",
    },
    {
      id: "3",
      name: "清明节",
      date: "2024-04-04",
      type: "农历" as const,
      lunarDate: "二月廿五",
      slug: "qingming-festival",
      metaDescription: "清明节是中国传统节日，也是二十四节气之一，人们在这一天祭祖扫墓、踏青郊游。",
    },
  ]

  // 将事件分组为过去、当前和未来
  const now = new Date()
  now.setHours(0, 0, 0, 0)

  const pastEvents = events.filter((event) => new Date(event.date) < now)
  const futureEvents = events.filter((event) => new Date(event.date) >= now)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center text-sm mb-6">
        <Link href="/" className="text-muted-foreground hover:text-foreground">
          首页
        </Link>
        <span className="mx-2 text-muted-foreground">/</span>
        <Link href="/tags" className="text-muted-foreground hover:text-foreground">
          标签
        </Link>
        <span className="mx-2 text-muted-foreground">/</span>
        <span className="text-foreground">{tagName}</span>
      </nav>

      {/* 返回按钮 */}
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/tags">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回标签列表
          </Link>
        </Button>
      </div>

      {/* 标签标题和信息 */}
      <div className="grid md:grid-cols-3 gap-8 mb-8">
        <div className="md:col-span-2">
          <h1 className="text-3xl font-bold mb-4 flex items-center gap-2">
            <Badge variant="outline" className="text-lg font-bold py-1 px-3">
              <TagIcon className="h-4 w-4 mr-2" />
              {tagName}
            </Badge>
          </h1>
          <p className="text-muted-foreground mb-4">浏览所有与"{tagName}"标签相关的事件。</p>
        </div>

        <div>
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">标签</h3>
                  <div className="px-3 py-2 rounded-md font-medium bg-muted">{tagName}</div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">事件总数</h3>
                  <div className="font-medium">{events.length} 个事件</div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">即将到来</h3>
                  <div className="font-medium">{futureEvents.length} 个事件</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 即将到来的事件 */}
      {futureEvents.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6">即将到来的"{tagName}"相关事件</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {futureEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </div>
      )}

      {/* 过去的事件 */}
      {pastEvents.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold mb-6">过去的"{tagName}"相关事件</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {pastEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </div>
      )}

      {/* 没有事件的情况 */}
      {events.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">没有找到与"{tagName}"标签相关的事件</p>
          <Button asChild>
            <Link href="/calendar">返回日历</Link>
          </Button>
        </div>
      )}
    </div>
  )
}
