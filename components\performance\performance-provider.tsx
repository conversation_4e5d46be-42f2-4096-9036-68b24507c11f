'use client'

/**
 * 性能监控提供者
 * 初始化和管理性能监控功能
 */

import { createContext, useContext, useEffect, ReactNode } from 'react'
import { initWebVitals } from '@/lib/performance/web-vitals'

interface PerformanceContextType {
  isMonitoring: boolean
  flushMetrics: () => void
}

const PerformanceContext = createContext<PerformanceContextType | null>(null)

export function usePerformance() {
  const context = useContext(PerformanceContext)
  if (!context) {
    throw new Error('usePerformance must be used within a PerformanceProvider')
  }
  return context
}

interface PerformanceProviderProps {
  children: ReactNode
  enableMonitoring?: boolean
}

export function PerformanceProvider({ 
  children, 
  enableMonitoring = process.env.NODE_ENV === 'production' 
}: PerformanceProviderProps) {
  useEffect(() => {
    if (!enableMonitoring) return
    
    // 初始化 Web Vitals 监控
    initWebVitals()
    
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn(`Long task detected: ${entry.duration}ms`, entry)
          }
        }
      })
      
      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] })
      } catch (e) {
        // longtask 可能不被支持
      }
      
      // 监控布局偏移
      const layoutShiftObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput && entry.value > 0.1) {
            console.warn(`Layout shift detected: ${entry.value}`, entry)
          }
        }
      })
      
      try {
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // layout-shift 可能不被支持
      }
      
      return () => {
        longTaskObserver.disconnect()
        layoutShiftObserver.disconnect()
      }
    }
  }, [enableMonitoring])
  
  const flushMetrics = () => {
    // 手动刷新指标
    if (typeof window !== 'undefined') {
      const { flushWebVitals } = require('@/lib/performance/web-vitals')
      flushWebVitals()
    }
  }
  
  const contextValue: PerformanceContextType = {
    isMonitoring: enableMonitoring,
    flushMetrics
  }
  
  return (
    <PerformanceContext.Provider value={contextValue}>
      {children}
    </PerformanceContext.Provider>
  )
}

/**
 * 性能监控 Hook
 */
export function usePerformanceMonitoring() {
  const { isMonitoring, flushMetrics } = usePerformance()
  
  const measureFunction = <T extends (...args: any[]) => any>(
    fn: T,
    name: string
  ): T => {
    if (!isMonitoring) return fn
    
    return ((...args: any[]) => {
      const start = performance.now()
      const result = fn(...args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - start
          if (duration > 100) {
            console.warn(`Slow function ${name}: ${duration}ms`)
          }
        })
      } else {
        const duration = performance.now() - start
        if (duration > 100) {
          console.warn(`Slow function ${name}: ${duration}ms`)
        }
        return result
      }
    }) as T
  }
  
  const measureComponent = (componentName: string) => {
    if (!isMonitoring) return
    
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      if (duration > 16) { // 超过一帧的时间
        console.warn(`Slow component render ${componentName}: ${duration}ms`)
      }
    }
  }
  
  return {
    isMonitoring,
    flushMetrics,
    measureFunction,
    measureComponent
  }
}
