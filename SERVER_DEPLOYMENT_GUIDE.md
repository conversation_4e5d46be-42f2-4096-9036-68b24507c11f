# 🚀 服务器部署指南

## 📋 推荐服务器配置

### 🖥️ **最低配置要求**
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 100Mbps

### 🏆 **推荐配置**
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps

## 🐧 **操作系统准备**

### **推荐：Ubuntu 22.04 LTS**

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git vim htop unzip

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用 Docker 组权限
newgrp docker
```

### **备选：CentOS 8/Rocky Linux**

```bash
# 更新系统
sudo dnf update -y

# 安装基础工具
sudo dnf install -y curl wget git vim htop unzip

# 安装 Docker
sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo dnf install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 📦 **部署步骤**

### **1. 准备项目文件**

```bash
# 在服务器上创建项目目录
mkdir -p /opt/calendar-app
cd /opt/calendar-app

# 上传项目文件 (使用 scp, rsync 或 git)
# 方法1: 使用 git
git clone https://github.com/your-username/calendar-app.git .

# 方法2: 使用 scp 从本地上传
# scp -r /path/to/local/calendar/* user@server:/opt/calendar-app/

# 方法3: 使用 rsync
# rsync -avz --exclude node_modules /path/to/local/calendar/ user@server:/opt/calendar-app/
```

### **2. 配置环境变量**

```bash
# 复制环境配置文件
cp .env.production .env

# 编辑配置文件
vim .env

# 必须修改的配置项：
# - POSTGRES_PASSWORD: 设置强密码
# - NEXTAUTH_SECRET: 生成32位随机字符串
# - NEXTAUTH_URL: 设置为你的域名
# - NEXT_PUBLIC_BASE_URL: 设置为你的域名
```

### **3. 配置域名和SSL**

```bash
# 编辑 Nginx 配置
vim nginx/sites-available/calendar.conf

# 将 your-domain.com 替换为你的实际域名
sed -i 's/your-domain.com/yourdomain.com/g' nginx/sites-available/calendar.conf
sed -i 's/your-domain.com/yourdomain.com/g' .env
```

### **4. 执行部署**

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署 (替换为你的域名)
./deploy.sh production yourdomain.com

# 或者手动部署
docker-compose up -d --build
```

### **5. 验证部署**

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 测试健康检查
curl http://localhost:3000/api/health

# 测试 HTTPS (如果配置了域名)
curl https://yourdomain.com/api/health
```

## 🔧 **配置详解**

### **环境变量说明**

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `POSTGRES_PASSWORD` | PostgreSQL 密码 | `your_secure_password` |
| `NEXTAUTH_SECRET` | NextAuth 密钥 | `32位随机字符串` |
| `NEXTAUTH_URL` | 应用完整URL | `https://yourdomain.com` |
| `NEXT_PUBLIC_BASE_URL` | 公开访问URL | `https://yourdomain.com` |

### **端口说明**

| 端口 | 服务 | 说明 |
|------|------|------|
| 80 | Nginx HTTP | 重定向到 HTTPS |
| 443 | Nginx HTTPS | 主要访问端口 |
| 3000 | Next.js App | 内部应用端口 |
| 5432 | PostgreSQL | 数据库端口 |
| 6379 | Redis | 缓存端口 |

## 🛡️ **安全配置**

### **防火墙设置**

```bash
# Ubuntu UFW
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### **SSL 证书自动续期**

```bash
# 添加 crontab 任务
crontab -e

# 添加以下行 (每月1号凌晨2点检查续期)
0 2 1 * * docker run --rm -v /opt/calendar-app/nginx/ssl:/etc/letsencrypt certbot/certbot renew --quiet && docker-compose restart nginx
```

## 📊 **监控和维护**

### **日志管理**

```bash
# 查看应用日志
docker-compose logs app

# 查看数据库日志
docker-compose logs postgres

# 查看 Nginx 日志
docker-compose logs nginx

# 实时监控所有日志
docker-compose logs -f
```

### **备份策略**

```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec -T postgres pg_dump -U calendar_user calendar_db > $BACKUP_DIR/db_backup_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz uploads/

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### **性能监控**

```bash
# 查看资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h

# 查看系统负载
htop
```

## 🚨 **故障排除**

### **常见问题**

1. **容器启动失败**
   ```bash
   docker-compose logs [service_name]
   docker-compose down && docker-compose up -d
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready -U calendar_user
   
   # 重启数据库
   docker-compose restart postgres
   ```

3. **SSL 证书问题**
   ```bash
   # 重新申请证书
   docker run --rm -v $(pwd)/nginx/ssl:/etc/letsencrypt \
     certbot/certbot certonly --standalone \
     -d yourdomain.com
   ```

4. **应用无响应**
   ```bash
   # 检查健康状态
   curl http://localhost:3000/api/health
   
   # 重启应用
   docker-compose restart app
   ```

## 📞 **获取支持**

如果遇到部署问题，请提供以下信息：
- 操作系统版本
- Docker 和 Docker Compose 版本
- 错误日志
- 配置文件内容（隐藏敏感信息）

---

**部署完成后，您的日历应用将在 `https://yourdomain.com` 上运行！**
