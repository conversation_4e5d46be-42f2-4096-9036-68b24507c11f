# 🚀 日历应用服务器部署方案

## 📋 **部署方案概述**

基于对您的日历项目的深入分析，我为您制定了完整的服务器部署方案。该方案采用现代化的容器化部署架构，确保高性能、高可用性和易维护性。

## 🏗️ **推荐技术架构**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Nginx         │    │   Next.js App    │    │  PostgreSQL     │
│  (反向代理)      │────│   (Docker)       │────│   (Docker)      │
│   SSL/HTTPS     │    │   PM2 进程管理    │    │   数据持久化     │
│   负载均衡       │    │   健康检查       │    │   自动备份       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                         ┌─────────────────┐
                         │     Redis       │
                         │    (缓存)       │
                         └─────────────────┘
```

## 🎯 **服务器环境推荐**

### **操作系统**: Ubuntu 22.04 LTS
**选择理由**:
- ✅ 更好的 Docker 生态支持
- ✅ PostgreSQL 官方支持完善
- ✅ 长期支持版本，稳定可靠
- ✅ 社区资源丰富，问题解决容易

### **最低配置要求**:
- **CPU**: 2核心
- **内存**: 4GB RAM  
- **存储**: 20GB SSD
- **网络**: 100Mbps

### **推荐配置**:
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps

## 📦 **已创建的部署文件**

我已经为您创建了完整的部署配置文件：

### **核心配置文件**
- `docker-compose.yml` - Docker 服务编排
- `Dockerfile` - 应用容器构建
- `.env.production` - 生产环境配置模板
- `nginx/nginx.conf` - Nginx 主配置
- `nginx/sites-available/calendar.conf` - 站点配置

### **部署脚本**
- `deploy.sh` - 完整部署脚本（包含 SSL 证书申请）
- `quick-deploy.sh` - 快速部署脚本（适合测试）

### **数据库配置**
- `scripts/init-database.sql` - 数据库初始化脚本
- 包含完整的表结构、索引、示例数据

### **健康检查**
- `app/api/health/route.ts` - 应用健康检查 API

## 🚀 **部署步骤**

### **方法一：快速部署（推荐用于测试）**

```bash
# 1. 上传项目文件到服务器
scp -r /path/to/calendar/* user@server:/opt/calendar-app/

# 2. 登录服务器
ssh user@server

# 3. 进入项目目录
cd /opt/calendar-app

# 4. 执行快速部署
chmod +x quick-deploy.sh
./quick-deploy.sh yourdomain.com
```

### **方法二：完整部署（推荐用于生产）**

```bash
# 1. 准备服务器环境（Ubuntu 22.04）
sudo apt update && sudo apt upgrade -y
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 2. 上传项目文件
# 3. 配置环境变量
cp .env.production .env
vim .env  # 修改必要配置

# 4. 执行完整部署
chmod +x deploy.sh
./deploy.sh production yourdomain.com
```

## 🔧 **关键配置说明**

### **数据库配置**
- **PostgreSQL 15**: 与 Supabase 完全兼容
- **自动初始化**: 包含完整表结构和示例数据
- **性能优化**: 已配置必要索引和查询优化

### **应用配置**
- **Next.js 15**: 使用 standalone 输出模式
- **PM2 集群**: 多进程部署，提高性能
- **健康检查**: 自动监控应用状态

### **Nginx 配置**
- **SSL/TLS**: 支持 Let's Encrypt 自动证书
- **Gzip 压缩**: 减少传输大小
- **静态文件缓存**: 优化加载速度
- **安全头部**: 完整的安全配置

## 📊 **部署后功能**

### **自动化功能**
- ✅ **数据库自动初始化**: 包含示例数据
- ✅ **SSL 证书管理**: Let's Encrypt 自动申请和续期
- ✅ **健康检查**: 自动监控服务状态
- ✅ **日志管理**: 结构化日志收集
- ✅ **备份策略**: 数据库和文件自动备份

### **性能优化**
- ✅ **Redis 缓存**: 提高响应速度
- ✅ **静态文件 CDN**: Nginx 静态文件服务
- ✅ **Gzip 压缩**: 减少带宽使用
- ✅ **数据库索引**: 优化查询性能

### **安全特性**
- ✅ **HTTPS 强制**: 自动重定向到安全连接
- ✅ **安全头部**: XSS、CSRF 防护
- ✅ **防火墙配置**: 只开放必要端口
- ✅ **容器隔离**: Docker 安全隔离

## 🛡️ **安全和维护**

### **监控指标**
- 应用响应时间
- 数据库连接状态
- 内存和 CPU 使用率
- 磁盘空间使用

### **备份策略**
- 数据库每日自动备份
- 上传文件定期备份
- 配置文件版本控制

### **日志管理**
- 应用日志：`docker-compose logs app`
- 数据库日志：`docker-compose logs postgres`
- Nginx 日志：`docker-compose logs nginx`

## 📞 **部署支持**

### **常见问题解决**
1. **容器启动失败**: 检查日志和配置
2. **数据库连接问题**: 验证环境变量
3. **SSL 证书问题**: 确认域名解析
4. **性能问题**: 监控资源使用

### **获取帮助**
如果遇到部署问题，请提供：
- 操作系统版本
- 错误日志内容
- 配置文件内容（隐藏敏感信息）

## 🎉 **部署完成后**

部署成功后，您的日历应用将具备：

- **高性能**: PM2 集群 + Redis 缓存
- **高可用**: Docker 容器自动重启
- **易维护**: 完整的日志和监控
- **安全可靠**: HTTPS + 安全头部配置
- **可扩展**: 支持水平扩展和负载均衡

**访问地址**: `https://yourdomain.com`
**管理面板**: `https://yourdomain.com/admin`
**健康检查**: `https://yourdomain.com/api/health`

---

**准备好开始部署了吗？请告诉我您的服务器信息，我将协助您完成部署！**
