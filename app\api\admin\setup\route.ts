import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

// 初始分类数据
const initialCategories = [
  {
    name: "公历",
    slug: "gregorian",
    color: "#0891b2",
    priority: 1,
  },
  {
    name: "农历",
    slug: "lunar",
    color: "#ca8a04",
    priority: 2,
  },
  {
    name: "重要事件",
    slug: "important-events",
    color: "#dc2626",
    priority: 3,
  },
  {
    name: "国际节日",
    slug: "international",
    color: "#7c3aed",
    priority: 4,
  },
  {
    name: "节气",
    slug: "solar-terms",
    color: "#15803d",
    priority: 5,
  },
  {
    name: "纪念日",
    slug: "memorial-days",
    color: "#4f46e5",
    priority: 6,
  },
  {
    name: "会展",
    slug: "exhibitions",
    color: "#06b6d4",
    priority: 7,
  },
  {
    name: "品牌日",
    slug: "brand-days",
    color: "#ea580c",
    priority: 8,
  },
  {
    name: "娱乐",
    slug: "entertainment",
    color: "#ec4899",
    priority: 9,
  },
]

// 初始事件数据
const initialEvents = [
  {
    name: "元旦",
    start_date: "2024-01-01",
    category_name: "公历",
  },
  {
    name: "春节",
    start_date: "2024-02-10",
    lunar_date: "正月初一",
    category_name: "农历",
  },
  {
    name: "元宵节",
    start_date: "2024-02-24",
    lunar_date: "正月十五",
    category_name: "农历",
  },
  {
    name: "清明节",
    start_date: "2024-04-04",
    lunar_date: "二月廿五",
    category_name: "农历",
  },
  {
    name: "劳动节",
    start_date: "2024-05-01",
    category_name: "公历",
  },
  {
    name: "端午节",
    start_date: "2024-06-10",
    lunar_date: "五月初五",
    category_name: "农历",
  },
  {
    name: "中秋节",
    start_date: "2024-09-17",
    lunar_date: "八月十五",
    category_name: "农历",
  },
  {
    name: "国庆节",
    start_date: "2024-10-01",
    category_name: "公历",
  },
]

export async function GET() {
  try {
    // 使用createClient函数，它会处理环境变量
    const supabase = createClient()

    // 检查是否能连接到Supabase
    const { error: connectionError } = await supabase.from("categories").select("count").single()

    if (connectionError && connectionError.code === "PGRST116") {
      // 表不存在
      return NextResponse.json(
        {
          error: "数据库表不存在",
          message: "请先访问 /api/admin/setup-tables 创建必要的数据库表",
        },
        { status: 400 },
      )
    }

    console.log("开始数据迁移...")

    // 2. 检查categories表是否为空
    const { data: categoriesCount, error: countError } = await supabase
      .from("categories")
      .select("*", { count: "exact", head: true })

    if (countError) {
      console.error("检查分类表错误:", countError)
      return NextResponse.json({ error: countError.message }, { status: 500 })
    }

    // 如果categories表为空，插入初始分类数据
    if (categoriesCount.count === 0) {
      console.log("插入初始分类数据...")
      const { error } = await supabase.from("categories").insert(initialCategories)
      if (error) {
        console.error("插入分类数据错误:", error)
        return NextResponse.json({ error: error.message }, { status: 500 })
      }
    } else {
      console.log("分类数据已存在，跳过插入")
    }

    // 3. 检查events表是否为空
    const { data: eventsCount, error: eventsCountError } = await supabase
      .from("events")
      .select("*", { count: "exact", head: true })

    if (eventsCountError) {
      console.error("检查事件表错误:", eventsCountError)
      return NextResponse.json({ error: eventsCountError.message }, { status: 500 })
    }

    // 如果events表为空，插入初始事件数据
    if (eventsCount.count === 0) {
      console.log("插入初始事件数据...")

      // 获取所有分类的ID映射
      const { data: categories, error: fetchCategoriesError } = await supabase.from("categories").select("id, name")

      if (fetchCategoriesError) {
        console.error("获取分类数据错误:", fetchCategoriesError)
        return NextResponse.json({ error: fetchCategoriesError.message }, { status: 500 })
      }

      const categoryMap = new Map(categories.map((c) => [c.name, c.id]))

      // 准备事件数据，替换分类名称为分类ID
      const eventsWithCategoryIds = initialEvents.map((event) => ({
        name: event.name,
        start_date: event.start_date,
        lunar_date: event.lunar_date,
        category_id: categoryMap.get(event.category_name),
      }))

      // 插入事件数据
      const { error: insertEventsError } = await supabase.from("events").insert(eventsWithCategoryIds)

      if (insertEventsError) {
        console.error("插入事件数据错误:", insertEventsError)
        return NextResponse.json({ error: insertEventsError.message }, { status: 500 })
      }
    } else {
      console.log("事件数据已存在，跳过插入")
    }

    console.log("数据迁移完成!")
    return NextResponse.json({ success: true, message: "数据迁移成功完成" })
  } catch (error) {
    console.error("数据迁移失败:", error)
    return NextResponse.json(
      {
        error: "数据迁移失败",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
