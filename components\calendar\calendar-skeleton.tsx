import { Skeleton } from "@/components/ui/skeleton"

export default function CalendarSkeleton() {
  return (
    <div className="space-y-6">
      {/* 信息概览骨架 */}
      <Skeleton className="h-12 w-full" />

      {/* 控制栏骨架 */}
      <div className="flex justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-9 rounded-md" />
          <Skeleton className="h-9 w-32 rounded-md" />
          <Skeleton className="h-9 w-9 rounded-md" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-9 w-9 rounded-md" />
          <Skeleton className="h-9 w-9 rounded-md" />
        </div>
      </div>

      {/* 日历网格骨架 */}
      <div className="grid grid-cols-7 gap-1">
        {/* 星期标题 */}
        {Array(7)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={`header-${i}`} className="h-8" />
          ))}

        {/* 日期单元格 */}
        {Array(35)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={`cell-${i}`} className="h-24" />
          ))}
      </div>
    </div>
  )
}
