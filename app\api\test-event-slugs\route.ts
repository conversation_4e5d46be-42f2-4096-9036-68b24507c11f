import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    console.log("Getting event slugs for testing...")
    
    const supabase = await createClient()
    
    // 获取一些事件的slug用于测试
    const { data: events, error } = await supabase
      .from("events")
      .select("*")
      .limit(5)
      .order("start_date", { ascending: true })
    
    if (error) {
      console.error("Error fetching event slugs:", error)
      return NextResponse.json({
        success: false,
        error: "Failed to fetch event slugs",
        details: error
      })
    }
    
    console.log("Event slugs fetched successfully:", events?.length)
    
    return NextResponse.json({
      success: true,
      message: "Event data fetched successfully",
      data: events,
      fields: events?.[0] ? Object.keys(events[0]) : [],
      testUrls: events?.map(event => ({
        name: event.name,
        id: event.id,
        url_slug: event.url_slug,
        hasSlug: !!event.url_slug,
        testUrl: event.url_slug ? `/event/${event.url_slug}` : `/event/${event.id}`,
        fullUrl: event.url_slug ? `http://localhost:3000/event/${event.url_slug}` : `http://localhost:3000/event/${event.id}`
      })) || []
    })
    
  } catch (error) {
    console.error("Test event slugs error:", error)
    return NextResponse.json({
      success: false,
      error: "Test failed",
      details: error instanceof Error ? error.message : String(error)
    })
  }
}
