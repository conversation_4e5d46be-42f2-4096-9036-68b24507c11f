import type { Event } from "./types"

// 模拟从数据库获取事件数据
export async function getEventsForMonth(year: number, month: number): Promise<Event[]> {
  // 这里应该是从数据库或API获取数据
  // 为了演示，我们生成一些示例数据

  const events: Event[] = [
    {
      id: "1",
      name: "元旦",
      date: `${year}-01-01`,
      type: "公历",
      description: "新年的第一天，世界多数国家通用的节日。",
    },
    {
      id: "2",
      name: "春节",
      date: `${year}-02-10`, // 2024年的春节日期，实际应该根据农历计算
      type: "农历",
      description: "中国最重要的传统节日，农历正月初一。",
      lunarDate: "正月初一",
    },
    {
      id: "3",
      name: "元宵节",
      date: `${year}-02-24`, // 2024年的元宵节日期
      type: "农历",
      description: "中国传统节日，农历正月十五。",
      lunarDate: "正月十五",
    },
    {
      id: "4",
      name: "国际妇女节",
      date: `${year}-03-08`,
      type: "国际节日",
      description: "纪念妇女权利的国际性节日。",
    },
    {
      id: "5",
      name: "植树节",
      date: `${year}-03-12`,
      type: "纪念日",
      description: "中国的植树节，鼓励全民义务植树。",
    },
    {
      id: "qingming",
      name: "清明节",
      date: `${year}-04-04`, // 2024年的清明节日期
      type: "农历",
      description: "中国传统节日，扫墓祭祖的日子。",
      lunarDate: "二月廿五",
      location: "全国各地",
      tags: ["传统文化", "农历节日", "二十四节气", "祭祀"],
    },
    {
      id: "7",
      name: "劳动节",
      date: `${year}-05-01`,
      type: "公历",
      description: "国际劳动节，又称五一国际劳动节。",
    },
    {
      id: "8",
      name: "母亲节",
      date: `${year}-05-12`, // 2024年的母亲节日期（5月第二个星期日）
      type: "国际节日",
      description: "感恩母亲的节日，每年5月第二个星期日。",
    },
    {
      id: "9",
      name: "端午节",
      date: `${year}-06-10`, // 2024年的端午节日期
      type: "农历",
      description: "中国传统节日，农历五月初五。",
      lunarDate: "五月初五",
    },
    {
      id: "10",
      name: "父亲节",
      date: `${year}-06-16`, // 2024年的父亲节日期（6月第三个星期日）
      type: "国际节日",
      description: "感恩父亲的节日，每年6月第三个星期日。",
    },
    {
      id: "11",
      name: "中国共产党成立日",
      date: `${year}-07-01`,
      type: "纪念日",
      description: "中国共产党成立纪念日。",
    },
    {
      id: "12",
      name: "七夕节",
      date: `${year}-08-10`, // 2024年的七夕节日期
      type: "农历",
      description: "中国传统的情人节，农历七月初七。",
      lunarDate: "七月初七",
    },
    {
      id: "13",
      name: "中秋节",
      date: `${year}-09-17`, // 2024年的中秋节日期
      type: "农历",
      description: "中国传统节日，农历八月十五。",
      lunarDate: "八月十五",
    },
    {
      id: "14",
      name: "国庆节",
      date: `${year}-10-01`,
      type: "公历",
      description: "中华人民共和国成立纪念日。",
    },
    {
      id: "15",
      name: "重阳节",
      date: `${year}-10-13`, // 2024年的重阳节日期
      type: "农历",
      description: "中国传统节日，农历九月初九。",
      lunarDate: "九月初九",
    },
    {
      id: "16",
      name: "万圣节",
      date: `${year}-10-31`,
      type: "国际节日",
      description: "西方传统节日，又称诸圣节。",
    },
    {
      id: "17",
      name: "双十一购物节",
      date: `${year}-11-11`,
      type: "品牌日",
      description: "中国最大的网络购物促销日。",
    },
    {
      id: "18",
      name: "感恩节",
      date: `${year}-11-28`, // 2024年的感恩节日期（11月第四个星期四）
      type: "国际节日",
      description: "美国传统节日，每年11月第四个星期四。",
    },
    {
      id: "19",
      name: "圣诞节",
      date: `${year}-12-25`,
      type: "国际节日",
      description: "基督教纪念耶稣诞生的节日。",
    },
    {
      id: "20",
      name: "除夕",
      date: `${year}-01-31`, // 2025年的除夕日期，实际应该根据农历计算
      type: "农历",
      description: "中国传统节日，农历腊月的最后一天。",
      lunarDate: "腊月三十",
    },
  ]

  // 为当前月份添加一些随机事件
  const daysInMonth = new Date(year, month, 0).getDate()

  for (let i = 1; i <= 5; i++) {
    const randomDay = Math.floor(Math.random() * daysInMonth) + 1
    const randomType: Event["type"] = ["纪念日", "会展"][Math.floor(Math.random() * 2)]

    events.push({
      id: `random-${month}-${i}`,
      name: `${month}月随机事件 ${i}`,
      date: `${year}-${month.toString().padStart(2, "0")}-${randomDay.toString().padStart(2, "0")}`,
      type: randomType,
      description: `这是一个${month}月的随机事件示例。`,
    })
  }

  return events
}

export async function getEventById(id: string): Promise<Event | null> {
  // 这里应该是从数据库或API获取数据
  // 为了演示，我们返回一个示例数据

  if (id === "qingming") {
    // 返回清明节的详细信息
    return {
      id: "qingming",
      name: "清明节",
      date: "2024-04-04", // 2024年的清明节日期
      type: "农历",
      description:
        "清明节是中国传统节日，也是二十四节气之一，通常在公历4月4日或5日，农历二月末或三月初。这一天，人们会祭祖扫墓、踏青郊游，缅怀逝去的亲人并迎接春天的到来。",
      lunarDate: "二月廿五",
      location: "全国各地",
      tags: ["传统文化", "农历节日", "二十四节气", "祭祀"],
    }
  }

  const events = await getEventsForMonth(new Date().getFullYear(), new Date().getMonth() + 1)
  return events.find((event) => event.id === id) || null
}

export async function getEventsByCategory(category: string): Promise<Event[]> {
  // 这里应该是从数据库或API获取数据
  // 为了演示，我们返回一些示例数据

  if (category === "农历") {
    // 返回农历类别的详细事件列表
    return [
      {
        id: "2",
        name: "春节",
        date: "2024-02-10",
        type: "农历",
        description: "中国最重要的传统节日，农历正月初一。",
        lunarDate: "正月初一",
        tags: ["传统文化", "农历节日", "家庭团聚"],
      },
      {
        id: "3",
        name: "元宵节",
        date: "2024-02-24",
        type: "农历",
        description: "中国传统节日，农历正月十五。",
        lunarDate: "正月十五",
        tags: ["传统文化", "农历节日", "灯笼"],
      },
      {
        id: "qingming",
        name: "清明节",
        date: "2024-04-04",
        type: "农历",
        description: "中国传统节日，扫墓祭祖的日子。",
        lunarDate: "二月廿五",
        location: "全国各地",
        tags: ["传统文化", "农历节日", "二十四节气", "祭祀"],
      },
      {
        id: "9",
        name: "端午节",
        date: "2024-06-10",
        type: "农历",
        description: "中国传统节日，农历五月初五。",
        lunarDate: "五月初五",
        tags: ["传统文化", "农历节日", "龙舟"],
      },
      {
        id: "12",
        name: "七夕节",
        date: "2024-08-10",
        type: "农历",
        description: "中国传统的情人节，农历七月初七。",
        lunarDate: "七月初七",
        tags: ["传统文化", "农历节日", "爱情"],
      },
      {
        id: "13",
        name: "中秋节",
        date: "2024-09-17",
        type: "农历",
        description: "中国传统节日，农历八月十五。",
        lunarDate: "八月十五",
        tags: ["传统文化", "农历节日", "月饼", "团圆"],
      },
      {
        id: "15",
        name: "重阳节",
        date: "2024-10-13",
        type: "农历",
        description: "中国传统节日，农历九月初九。",
        lunarDate: "九月初九",
        tags: ["传统文化", "农历节日", "敬老"],
      },
      {
        id: "20",
        name: "除夕",
        date: "2024-01-31",
        type: "农历",
        description: "中国传统节日，农历腊月的最后一天。",
        lunarDate: "腊月三十",
        tags: ["传统文化", "农历节日", "家庭团聚", "年夜饭"],
      },
    ]
  }

  const allEvents = await getEventsForMonth(new Date().getFullYear(), new Date().getMonth() + 1)
  return allEvents.filter((event) => event.type === category)
}

export async function getEventsByTag(tag: string): Promise<Event[]> {
  // 这里应该是从数据库或API获取数据
  // 为了演示，我们返回一些示例数据

  if (tag === "传统文化") {
    // 返回传统文化标签的详细事件列表
    return [
      {
        id: "2",
        name: "春节",
        date: "2024-02-10",
        type: "农历",
        description: "中国最重要的传统节日，农历正月初一。",
        lunarDate: "正月初一",
        tags: ["传统文化", "农历节日", "家庭团聚"],
      },
      {
        id: "3",
        name: "元宵节",
        date: "2024-02-24",
        type: "农历",
        description: "中国传统节日，农历正月十五。",
        lunarDate: "正月十五",
        tags: ["传统文化", "农历节日", "灯笼"],
      },
      {
        id: "qingming",
        name: "清明节",
        date: "2024-04-04",
        type: "农历",
        description: "中国传统节日，扫墓祭祖的日子。",
        lunarDate: "二月廿五",
        location: "全国各地",
        tags: ["传统文化", "农历节日", "二十四节气", "祭祀"],
      },
      {
        id: "9",
        name: "端午节",
        date: "2024-06-10",
        type: "农历",
        description: "中国传统节日，农历五月初五。",
        lunarDate: "五月初五",
        tags: ["传统文化", "农历节日", "龙舟"],
      },
      {
        id: "12",
        name: "七夕节",
        date: "2024-08-10",
        type: "农历",
        description: "中国传统的情人节，农历七月初七。",
        lunarDate: "七月初七",
        tags: ["传统文化", "农历节日", "爱情"],
      },
      {
        id: "13",
        name: "中秋节",
        date: "2024-09-17",
        type: "农历",
        description: "中国传统节日，农历八月十五。",
        lunarDate: "八月十五",
        tags: ["传统文化", "农历节日", "月饼", "团圆"],
      },
    ]
  }

  const allEvents = await getEventsForMonth(new Date().getFullYear(), new Date().getMonth() + 1)
  return allEvents.filter((event) => event.tags?.includes(tag))
}
