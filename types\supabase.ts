export type Json = string | number | boolean | null | { [key: string]: J<PERSON> | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          color: string | null
          priority: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          color?: string | null
          priority?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          color?: string | null
          priority?: number | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      events: {
        Row: {
          id: string
          name: string
          start_date: string
          lunar_date: string | null
          category_id: string
          image_url: string | null
          image_alt_text: string | null
          meta_title: string | null
          meta_description: string | null
          url_slug: string
          h1_title: string | null
          ai_summary: string | null
          ai_faq: Json | null
          is_recurring: boolean
          priority: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          start_date: string
          lunar_date?: string | null
          category_id: string
          image_url?: string | null
          image_alt_text?: string | null
          meta_title?: string | null
          meta_description?: string | null
          url_slug: string
          h1_title?: string | null
          ai_summary?: string | null
          ai_faq?: Json | null
          is_recurring?: boolean
          priority?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          start_date?: string
          lunar_date?: string | null
          category_id?: string
          image_url?: string | null
          image_alt_text?: string | null
          meta_title?: string | null
          meta_description?: string | null
          url_slug?: string
          h1_title?: string | null
          ai_summary?: string | null
          ai_faq?: Json | null
          is_recurring?: boolean
          priority?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      tags: {
        Row: {
          id: string
          name: string
          slug: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      event_tags: {
        Row: {
          event_id: string
          tag_id: string
        }
        Insert: {
          event_id: string
          tag_id: string
        }
        Update: {
          event_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_tags_event_id_fkey"
            columns: ["event_id"]
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_tags_tag_id_fkey"
            columns: ["tag_id"]
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_events_by_category: {
        Args: {
          category_slug: string
        }
        Returns: {
          id: string
          name: string
          start_date: string
          lunar_date: string | null
          category_id: string
          image_url: string | null
          image_alt_text: string | null
          meta_title: string | null
          meta_description: string | null
          url_slug: string
          h1_title: string | null
          ai_summary: string | null
          ai_faq: Json | null
          is_recurring: boolean
          priority: number
          status: string
          created_at: string
          updated_at: string
        }[]
      }
      search_events: {
        Args: {
          query_text: string
        }
        Returns: {
          id: string
          name: string
          start_date: string
          url_slug: string
          category_id: string
          rank: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
