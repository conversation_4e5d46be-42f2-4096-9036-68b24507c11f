"use client"

import { useState, useEffect, useCallback } from "react"

interface CacheOptions {
  key: string
  expiryMinutes?: number
}

interface PaginationOptions {
  pageSize?: number
  initialPage?: number
}

export function useCachedData<T>(
  fetchFn: () => Promise<T>,
  cacheOptions: CacheOptions,
  paginationOptions?: PaginationOptions,
) {
  const [data, setData] = useState<T | null>(null)
  const [paginatedData, setPaginatedData] = useState<any[] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [page, setPage] = useState(paginationOptions?.initialPage || 1)
  const pageSize = paginationOptions?.pageSize || 20

  // 从缓存获取数据
  const getFromCache = useCallback(() => {
    if (typeof window === "undefined") return null

    try {
      const cachedItem = localStorage.getItem(cacheOptions.key)
      if (!cachedItem) return null

      const { data, timestamp } = JSON.parse(cachedItem)
      const expiryMinutes = cacheOptions.expiryMinutes || 5
      const expiryTime = expiryMinutes * 60 * 1000

      // 检查缓存是否过期
      if (Date.now() - timestamp > expiryTime) {
        localStorage.removeItem(cacheOptions.key)
        return null
      }

      return data
    } catch (err) {
      console.error("读取缓存时出错:", err)
      return null
    }
  }, [cacheOptions.key, cacheOptions.expiryMinutes])

  // 保存到缓存
  const saveToCache = useCallback(
    (data: T) => {
      if (typeof window === "undefined") return

      try {
        const item = {
          data,
          timestamp: Date.now(),
        }
        localStorage.setItem(cacheOptions.key, JSON.stringify(item))
      } catch (err) {
        console.error("保存到缓存时出错:", err)
      }
    },
    [cacheOptions.key],
  )

  // 获取数据
  const fetchData = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // 先尝试从缓存获取
      const cachedData = getFromCache()
      if (cachedData) {
        setData(cachedData)
        setIsLoading(false)
        return
      }

      // 获取新数据
      const freshData = await fetchFn()
      setData(freshData)
      saveToCache(freshData)
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)))
      console.error("获取数据时出错:", err)
    } finally {
      setIsLoading(false)
    }
  }, [fetchFn, getFromCache, saveToCache])

  // 处理分页
  useEffect(() => {
    if (!data || !Array.isArray(data)) {
      setPaginatedData(null)
      return
    }

    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    // @ts-ignore - 我们已经检查了data是一个数组
    const slicedData = data.slice(startIndex, endIndex)
    setPaginatedData(slicedData)
  }, [data, page, pageSize])

  // 初始数据获取
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // 刷新函数
  const refresh = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  // 分页控制
  const nextPage = useCallback(() => {
    if (!data || !Array.isArray(data)) return
    // @ts-ignore - 我们已经检查了data是一个数组
    const totalPages = Math.ceil(data.length / pageSize)
    if (page < totalPages) {
      setPage((prev) => prev + 1)
    }
  }, [data, page, pageSize])

  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage((prev) => prev - 1)
    }
  }, [page])

  const goToPage = useCallback(
    (pageNum: number) => {
      if (!data || !Array.isArray(data)) return
      // @ts-ignore - 我们已经检查了data是一个数组
      const totalPages = Math.ceil(data.length / pageSize)
      if (pageNum >= 1 && pageNum <= totalPages) {
        setPage(pageNum)
      }
    },
    [data, pageSize],
  )

  // 分页信息
  const pagination = {
    currentPage: page,
    totalPages: data && Array.isArray(data) ? Math.ceil((data as any[]).length / pageSize) : 0,
    pageSize,
    nextPage,
    prevPage,
    goToPage,
  }

  return {
    data: paginationOptions ? paginatedData : data,
    isLoading,
    error,
    refresh,
    pagination,
    fullData: data,
  }
}
