"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { DURATIONS, EASINGS } from "@/lib/animations/config"

interface SharedElementProps {
  layoutId: string
  children: React.ReactNode
  className?: string
  onClick?: () => void
  href?: string
  style?: React.CSSProperties
}

// 共享元素组件
export function SharedElement({ 
  layoutId, 
  children, 
  className = "",
  onClick,
  href,
  style,
  ...props 
}: SharedElementProps) {
  const router = useRouter()
  const [isNavigating, setIsNavigating] = useState(false)

  const handleClick = () => {
    if (href) {
      setIsNavigating(true)
      // 添加小延迟让动画开始
      setTimeout(() => {
        router.push(href)
      }, 50)
    }
    onClick?.()
  }

  return (
    <motion.div
      layoutId={layoutId}
      className={className}
      style={style}
      onClick={handleClick}
      whileHover={href ? { 
        scale: 1.02,
        transition: { duration: 0.2 }
      } : {}}
      whileTap={href ? { 
        scale: 0.98,
        transition: { duration: 0.1 }
      } : {}}
      animate={isNavigating ? {
        scale: 1.05,
        zIndex: 50
      } : {}}
      transition={{
        type: "spring",
        damping: 25,
        stiffness: 300,
        duration: DURATIONS.slow / 1000
      }}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// 事件卡片的共享元素
export function EventCardShared({
  event,
  children,
  className = "",
  ...props
}: {
  event: { id: string; slug?: string }
  children: React.ReactNode
  className?: string
  [key: string]: any
}) {
  const eventUrl = `/event/${encodeURIComponent(event.slug || event.id)}`
  
  return (
    <SharedElement
      layoutId={`event-card-${event.id}`}
      href={eventUrl}
      className={`cursor-pointer transition-all ${className}`}
      {...props}
    >
      {children}
    </SharedElement>
  )
}

// 页面过渡容器
export function PageTransition({
  children,
  className = ""
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: DURATIONS.normal / 1000,
        ease: EASINGS.decelerate
      }}
    >
      {children}
    </motion.div>
  )
}

// 事件详情页头部的共享元素
export function EventDetailHeader({
  event,
  children,
  className = ""
}: {
  event: { id: string }
  children: React.ReactNode
  className?: string
}) {
  return (
    <motion.div
      layoutId={`event-card-${event.id}`}
      className={className}
      initial={{ borderRadius: "8px" }}
      animate={{ borderRadius: "0px" }}
      transition={{
        type: "spring",
        damping: 25,
        stiffness: 300,
        duration: DURATIONS.slow / 1000
      }}
    >
      {children}
    </motion.div>
  )
}

// 路由过渡包装器
export function RouteTransition({
  children,
  routeKey
}: {
  children: React.ReactNode
  routeKey: string
}) {
  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={routeKey}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 1.05 }}
        transition={{
          duration: DURATIONS.normal / 1000,
          ease: EASINGS.standard
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// 模态框过渡
export function ModalTransition({
  children,
  isOpen,
  onClose
}: {
  children: React.ReactNode
  isOpen: boolean
  onClose?: () => void
}) {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black/50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* 模态框内容 */}
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{
              type: "spring",
              damping: 25,
              stiffness: 300
            }}
          >
            {children}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

// 悬浮卡片效果
export function HoverCard({
  children,
  className = "",
  hoverScale = 1.02,
  ...props
}: {
  children: React.ReactNode
  className?: string
  hoverScale?: number
  [key: string]: any
}) {
  return (
    <motion.div
      className={className}
      whileHover={{ 
        scale: hoverScale,
        y: -2,
        boxShadow: "0 10px 25px rgba(0, 0, 0, 0.15)"
      }}
      transition={{
        duration: 0.2,
        ease: "easeOut"
      }}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// 错开动画容器
export function StaggerContainer({
  children,
  className = "",
  staggerDelay = 0.1
}: {
  children: React.ReactNode
  className?: string
  staggerDelay?: number
}) {
  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            staggerChildren: staggerDelay,
            delayChildren: 0.1
          }
        }
      }}
    >
      {children}
    </motion.div>
  )
}

// 错开动画子项
export function StaggerItem({
  children,
  className = ""
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <motion.div
      className={className}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { 
          opacity: 1, 
          y: 0,
          transition: {
            duration: 0.4,
            ease: "easeOut"
          }
        }
      }}
    >
      {children}
    </motion.div>
  )
}
