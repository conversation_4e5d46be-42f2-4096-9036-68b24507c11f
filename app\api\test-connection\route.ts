import { NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET() {
  try {
    console.log("Testing Supabase connection...")

    // 检查环境变量
    const url = process.env.NEXT_PUBLIC_SUPABASE_URL
    const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    console.log("Environment variables:", {
      url: url ? "✓ Set" : "✗ Missing",
      key: key ? "✓ Set" : "✗ Missing"
    })

    if (!url || !key) {
      return NextResponse.json({
        success: false,
        error: "Missing environment variables",
        details: { url: !!url, key: !!key }
      })
    }

    // 创建客户端
    const supabase = await createClient()
    console.log("Supabase client created:", typeof supabase, Object.keys(supabase))

    // 测试简单查询
    const { data, error } = await supabase
      .from("categories")
      .select("id, name")
      .limit(5)

    if (error) {
      console.error("Query error:", error)
      return NextResponse.json({
        success: false,
        error: "Query failed",
        details: error
      })
    }

    console.log("Query successful, data:", data)

    return NextResponse.json({
      success: true,
      message: "Connection successful",
      data: data,
      count: data?.length || 0
    })

  } catch (error) {
    console.error("Test connection error:", error)
    return NextResponse.json({
      success: false,
      error: "Connection test failed",
      details: error instanceof Error ? error.message : String(error)
    })
  }
}


