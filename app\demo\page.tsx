import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import DayEventsDemo from "@/components/demo/day-events-demo"
import { getCategoryColor } from "@/lib/utils"
import type { EventType } from "@/lib/types"

export const metadata: Metadata = {
  title: "事件日历演示",
  description: "查看事件日历的样式演示",
}

export default function DemoPage() {
  // 生成一个随机日期（2024年内）
  const randomMonth = Math.floor(Math.random() * 12) + 1
  const daysInMonth = new Date(2024, randomMonth, 0).getDate()
  const randomDay = Math.floor(Math.random() * daysInMonth) + 1

  const demoDate = new Date(2024, randomMonth - 1, randomDay)

  // 所有事件类型
  const allCategories: EventType[] = [
    "公历",
    "农历",
    "重要事件",
    "国际节日",
    "节气",
    "纪念日",
    "会展",
    "品牌日",
    "娱乐",
  ]

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">事件日历演示</h1>
        <p className="text-muted-foreground">
          以下是{demoDate.toLocaleDateString("zh-CN", { year: "numeric", month: "long", day: "numeric" })}
          的10个随机事件示例，用于展示日历单元格中的事件显示效果。
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>日历单元格演示</CardTitle>
          </CardHeader>
          <CardContent>
            <DayEventsDemo date={demoDate} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>事件类型颜色说明</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {allCategories.map((category) => (
                <div key={category} className="flex items-center gap-2">
                  <Badge style={{ backgroundColor: getCategoryColor(category) }} className="text-white">
                    {category}
                  </Badge>
                  <span className="text-sm">优先级: {getPriorityText(category)}</span>
                </div>
              ))}
            </div>
            <div className="mt-6">
              <p className="text-sm text-muted-foreground mb-2">
                日历单元格中最多显示5个事件，超出部分需点击"..."按钮查看。事件按优先级排序显示。
              </p>
              <Button asChild className="mt-4">
                <Link href="/calendar">返回日历</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}

function getPriorityText(category: EventType): string {
  switch (category) {
    case "公历":
      return "最高"
    case "农历":
      return "很高"
    case "重要事件":
      return "高"
    case "国际节日":
      return "中高"
    case "节气":
      return "中"
    case "纪念日":
      return "中低"
    case "会展":
      return "低"
    case "品牌日":
      return "较低"
    case "娱乐":
      return "最低"
    default:
      return ""
  }
}
