"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Calendar, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { getCategoryColor, sortEventsByPriority } from "@/lib/utils"
import { EVENT_TYPE_PRIORITY } from "@/lib/types"
import type { Event } from "@/lib/types"
import { getSimpleLunarDate, getSpecialLunarEvents } from "@/lib/utils/lunar-calendar"

interface DayViewProps {
  year: number
  month: number
  events: Event[]
  onBackToMonthView: () => void
}

export default function DayView({ year, month, events, onBackToMonthView }: DayViewProps) {
  // 默认选择当前日期或月份的第一天
  const [selectedDate, setSelectedDate] = useState(() => {
    const today = new Date()
    if (today.getFullYear() === year && today.getMonth() + 1 === month) {
      return today
    }
    return new Date(year, month - 1, 1)
  })

  // 获取选中日期的事件
  const selectedDateEvents = events.filter((event) => {
    const eventDate = new Date(event.date)
    return (
      eventDate.getDate() === selectedDate.getDate() &&
      eventDate.getMonth() === selectedDate.getMonth() &&
      eventDate.getFullYear() === selectedDate.getFullYear()
    )
  })

  // 按优先级排序事件
  const sortedEvents = sortEventsByPriority(selectedDateEvents, EVENT_TYPE_PRIORITY)

  // 获取农历日期
  const lunarDate = (() => {
    // 查找当天是否有带农历日期的事件
    const eventWithLunarDate = selectedDateEvents.find((event) => event.lunarDate)
    if (eventWithLunarDate?.lunarDate) {
      return eventWithLunarDate.lunarDate
    }
    // 如果没有事件带农历日期，则使用计算的农历日期
    return getSpecialLunarEvents(selectedDate) || getSimpleLunarDate(selectedDate)
  })()

  // 导航到前一天
  const goToPreviousDay = () => {
    const prevDate = new Date(selectedDate)
    prevDate.setDate(prevDate.getDate() - 1)
    setSelectedDate(prevDate)
  }

  // 导航到后一天
  const goToNextDay = () => {
    const nextDate = new Date(selectedDate)
    nextDate.setDate(nextDate.getDate() + 1)
    setSelectedDate(nextDate)
  }

  // 检查是否是今天
  const isToday = () => {
    const today = new Date()
    return (
      today.getDate() === selectedDate.getDate() &&
      today.getMonth() === selectedDate.getMonth() &&
      today.getFullYear() === selectedDate.getFullYear()
    )
  }

  // 导航到今天
  const goToToday = () => {
    setSelectedDate(new Date())
  }

  return (
    <div className="space-y-4">
      {/* 返回月视图按钮 */}
      <div className="flex justify-between items-center">
        <Button variant="outline" size="sm" onClick={onBackToMonthView}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回月视图
        </Button>
        {!isToday() && (
          <Button variant="outline" size="sm" onClick={goToToday}>
            <Calendar className="h-4 w-4 mr-2" />
            今天
          </Button>
        )}
      </div>

      {/* 日期导航 */}
      <div className="flex justify-between items-center bg-muted p-3 rounded-lg">
        <Button variant="ghost" size="icon" onClick={goToPreviousDay} aria-label="前一天">
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="text-center">
          <h2 className="text-lg font-semibold">
            {selectedDate.toLocaleDateString("zh-CN", {
              year: "numeric",
              month: "long",
              day: "numeric",
              weekday: "long",
            })}
          </h2>
          <p className="text-sm text-muted-foreground mt-1">农历 {lunarDate}</p>
        </div>
        <Button variant="ghost" size="icon" onClick={goToNextDay} aria-label="后一天">
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>

      {/* 事件列表 */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold mb-4">今日事件 ({sortedEvents.length})</h3>
          {sortedEvents.length > 0 ? (
            <div className="space-y-3">
              {sortedEvents.map((event) => (
                <Link key={event.id} href={`/event/${encodeURIComponent(event.slug || event.id)}`}>
                  <div className="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/50 active:bg-muted transition-colors">
                    <Badge style={{ backgroundColor: getCategoryColor(event.type) }} className="text-white shrink-0">
                      {event.type}
                    </Badge>
                    <div>
                      <div className="font-medium">{event.name}</div>
                      {event.description && (
                        <div className="text-sm text-muted-foreground mt-1">{event.description}</div>
                      )}
                      {event.lunarDate && (
                        <div className="text-xs text-muted-foreground mt-1">农历: {event.lunarDate}</div>
                      )}
                      {event.tags && event.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {event.tags.map((tag, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>今天没有事件</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 简单的月份日历导航 */}
      <div className="mt-4">
        <h3 className="text-sm font-medium mb-2">快速导航</h3>
        <div className="grid grid-cols-7 gap-1">
          {Array.from({ length: new Date(year, month, 0).getDate() }, (_, i) => i + 1).map((day) => {
            const date = new Date(year, month - 1, day)
            const hasEvents = events.some((event) => {
              const eventDate = new Date(event.date)
              return (
                eventDate.getDate() === day && eventDate.getMonth() === month - 1 && eventDate.getFullYear() === year
              )
            })

            const isSelected =
              selectedDate.getDate() === day &&
              selectedDate.getMonth() === month - 1 &&
              selectedDate.getFullYear() === year

            const isCurrentDay =
              new Date().getDate() === day && new Date().getMonth() === month - 1 && new Date().getFullYear() === year

            return (
              <Button
                key={day}
                variant={isSelected ? "default" : isCurrentDay ? "outline" : "ghost"}
                className={`h-8 w-8 p-0 ${hasEvents ? "font-bold" : ""}`}
                onClick={() => setSelectedDate(date)}
              >
                {day}
              </Button>
            )
          })}
        </div>
      </div>
    </div>
  )
}
